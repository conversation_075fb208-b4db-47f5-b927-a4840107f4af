<template>
  <div class="page-container">
    <el-card>
      <template #header>
        <span>一点一策</span>
      </template>
      <div class="strategy-content">
        <div class="chart-placeholder">
          <el-icon size="64"><Guide /></el-icon>
          <p>一点一策管理</p>
        </div>
      </div>
    </el-card>
  </div>
</template>

<script setup lang="ts">
import { Guide } from '@element-plus/icons-vue'
</script>

<style scoped>
.page-container {
  padding: 20px;
}
.strategy-content {
  height: 400px;
}
.chart-placeholder {
  height: 100%;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  color: #909399;
  background-color: #f5f7fa;
}
</style>
