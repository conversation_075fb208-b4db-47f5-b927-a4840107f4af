<template>
  <div class="page-container">
    <el-card>
      <template #header>
        <span>区域排名展示</span>
      </template>
      <div class="ranking-content">
        <div class="chart-placeholder">
          <el-icon size="64"><Histogram /></el-icon>
          <p>区域排名图表展示</p>
        </div>
      </div>
    </el-card>
  </div>
</template>

<script setup lang="ts">
import { Histogram } from '@element-plus/icons-vue'
</script>

<style scoped>
.page-container {
  padding: 20px;
}
.ranking-content {
  height: 400px;
}
.chart-placeholder {
  height: 100%;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  color: #909399;
  background-color: #f5f7fa;
}
</style>
