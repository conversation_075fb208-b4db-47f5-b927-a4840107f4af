<template>
  <el-dialog
    v-model="dialogVisible"
    title="政策库详情"
    width="1200px"
    :close-on-click-modal="false"
  >
    <div v-if="policyData" class="detail-container">
      <!-- 基本信息 -->
      <div class="detail-section">
        <h3 class="section-title">基本信息</h3>
        <el-row :gutter="20">
          <el-col :span="12">
            <div class="detail-item">
              <label class="detail-label">政策名称：</label>
              <span class="detail-value">{{ policyData.policyName }}</span>
            </div>
          </el-col>
          <el-col :span="12">
            <div class="detail-item">
              <label class="detail-label">发布部门：</label>
              <span class="detail-value">{{ policyData.publishDepartment }}</span>
            </div>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="12">
            <div class="detail-item">
              <label class="detail-label">发布日期：</label>
              <span class="detail-value">{{ policyData.publishDate }}</span>
            </div>
          </el-col>
          <el-col :span="12">
            <div class="detail-item">
              <label class="detail-label">实施日期：</label>
              <span class="detail-value">{{ policyData.implementationDate }}</span>
            </div>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="12">
            <div class="detail-item">
              <label class="detail-label">政策层级：</label>
              <span class="detail-value">{{ policyData.policyLevelName }}</span>
            </div>
          </el-col>
          <el-col :span="12">
            <div class="detail-item">
              <label class="detail-label">政策类型：</label>
              <span class="detail-value">{{ policyData.policyTypeName }}</span>
            </div>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="12">
            <div class="detail-item">
              <label class="detail-label">政策编号：</label>
              <span class="detail-value">{{ policyData.policyNumber }}</span>
            </div>
          </el-col>
          <el-col :span="12">
            <div class="detail-item">
              <label class="detail-label">数据状态：</label>
              <el-tag :type="getStatusTagType(policyData.dataStatus)">
                {{ policyData.dataStatusName }}
              </el-tag>
            </div>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="12">
            <div class="detail-item">
              <label class="detail-label">失效日期：</label>
              <span class="detail-value">{{ policyData.expiryDate || '长期有效' }}</span>
            </div>
          </el-col>
          <el-col :span="12">
            <div class="detail-item">
              <label class="detail-label">更新时间：</label>
              <span class="detail-value">{{ policyData.updateTime || policyData.inputTime }}</span>
            </div>
          </el-col>
        </el-row>
      </div>

      <!-- 详细信息 -->
      <div class="detail-section">
        <h3 class="section-title">详细信息</h3>

        <div class="detail-item">
          <label class="detail-label">适用地区：</label>
          <div class="detail-value">
            <el-tag
              v-for="region in policyData.applicableRegionList"
              :key="region"
              class="region-tag"
            >
              {{ region }}
            </el-tag>
            <span
              v-if="
                !policyData.applicableRegionList || policyData.applicableRegionList.length === 0
              "
            >
              全国
            </span>
          </div>
        </div>

        <div class="detail-item">
          <label class="detail-label">核心主题：</label>
          <div class="detail-value detail-text">{{ policyData.coreTheme }}</div>
        </div>

        <div class="detail-item">
          <label class="detail-label">涉及污染物：</label>
          <div class="detail-value">
            <el-tag
              v-for="pollutant in policyData.controlledPollutantsList"
              :key="pollutant"
              type="warning"
              class="pollutant-tag"
            >
              {{ pollutant }}
            </el-tag>
            <span
              v-if="
                !policyData.controlledPollutantsList ||
                policyData.controlledPollutantsList.length === 0
              "
            >
              无特定污染物
            </span>
          </div>
        </div>

        <div class="detail-item">
          <label class="detail-label">管控对象：</label>
          <div class="detail-value detail-text">{{ policyData.controlTargets }}</div>
        </div>

        <div v-if="policyData.remarks" class="detail-item">
          <label class="detail-label">备注：</label>
          <div class="detail-value detail-text">{{ policyData.remarks }}</div>
        </div>
      </div>

      <!-- 附件信息 -->
      <div v-if="policyData.fileList && policyData.fileList.length > 0" class="detail-section">
        <h3 class="section-title">附件信息</h3>
        <div class="file-list">
          <div v-for="file in policyData.fileList" :key="file.fileId" class="file-item">
            <div class="file-info">
              <el-icon class="file-icon"><Document /></el-icon>
              <span class="file-name">{{ file.fileName }}</span>
            </div>
            <div class="file-actions">
              <el-button type="primary" size="small" @click="handlePreview(file)">
                <el-icon><View /></el-icon>
                预览
              </el-button>
              <el-button size="small" @click="handleDownload(file)">
                <el-icon><Download /></el-icon>
                下载
              </el-button>
            </div>
          </div>
        </div>
      </div>

      <!-- 操作记录 -->
      <div class="detail-section">
        <h3 class="section-title">操作记录</h3>
        <div class="operation-record">
          <div class="record-item">
            <div class="record-time">{{ policyData.inputTime }}</div>
            <div class="record-action">创建</div>
            <div class="record-user">{{ policyData.inputUser || '系统' }}</div>
          </div>
          <div v-if="policyData.updateTime" class="record-item">
            <div class="record-time">{{ policyData.updateTime }}</div>
            <div class="record-action">更新</div>
            <div class="record-user">{{ policyData.updateUser || '系统' }}</div>
          </div>
        </div>
      </div>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">关闭</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import { Document, View, Download } from '@element-plus/icons-vue'
import type { PolicyInfo, PolicyFile } from '@/api/types'
import { previewFileAuto, downloadFile, type FileInfo } from '@/utils/fileUtils'

// Props
interface Props {
  visible: boolean
  policyData?: PolicyInfo | null
}

// Emits
interface Emits {
  (e: 'update:visible', value: boolean): void
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()

// 计算属性
const dialogVisible = computed({
  get: () => props.visible,
  set: (value) => emit('update:visible', value),
})

// 获取状态标签类型
const getStatusTagType = (status: number) => {
  switch (status) {
    case 1:
      return 'success' // 正常
    case 2:
      return 'info' // 草稿
    case 3:
      return 'warning' // 待审核
    case -1:
      return 'danger' // 失效
    default:
      return 'info'
  }
}

// 预览文件
const handlePreview = (file: PolicyFile) => {
  const fileInfo: FileInfo = {
    fileId: file.fileId,
    fileName: file.fileName,
    url: file.url,
    filePath: file.filePath,
    fileType: file.fileType,
  }
  previewFileAuto(fileInfo)
}

// 下载文件
const handleDownload = (file: PolicyFile) => {
  const fileInfo: FileInfo = {
    fileId: file.fileId,
    fileName: file.fileName,
    url: file.url,
    filePath: file.filePath,
    fileType: file.fileType,
  }
  downloadFile(fileInfo, { useBlob: true })
}

// 关闭对话框
const handleClose = () => {
  dialogVisible.value = false
}
</script>

<style scoped lang="scss">
.detail-container {
  padding: 0 30px;
  max-height: 70vh;
  overflow-y: auto;
}

.detail-section {
  margin-bottom: 30px;
  padding-bottom: 20px;
  border-bottom: 1px solid #f0f0f0;

  &:last-child {
    border-bottom: none;
    margin-bottom: 0;
  }
}

.section-title {
  font-size: 16px;
  font-weight: 600;
  color: #303133;
  margin-bottom: 20px;
  padding-bottom: 8px;
  border-bottom: 2px solid #409eff;
}

.detail-item {
  margin-bottom: 16px;
  display: flex;
  align-items: flex-start;

  .detail-label {
    min-width: 120px;
    font-weight: 500;
    color: #606266;
    margin-right: 12px;
    flex-shrink: 0;
  }

  .detail-value {
    flex: 1;
    color: #303133;
    word-break: break-all;

    &.detail-text {
      line-height: 1.6;
      white-space: pre-wrap;
    }
  }
}

.region-tag,
.pollutant-tag {
  margin-right: 8px;
  margin-bottom: 4px;
}

.file-list {
  .file-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 12px;
    background: #f8f9fa;
    border-radius: 6px;
    margin-bottom: 8px;
    transition: background-color 0.3s ease;

    &:hover {
      background: #e9ecef;
    }

    .file-info {
      display: flex;
      align-items: center;
      flex: 1;

      .file-icon {
        font-size: 20px;
        color: #409eff;
        margin-right: 8px;
      }

      .file-name {
        font-weight: 500;
        color: #303133;
      }
    }

    .file-actions {
      display: flex;
      gap: 8px;
    }
  }
}

.operation-record {
  .record-item {
    display: flex;
    align-items: center;
    padding: 8px 0;
    border-bottom: 1px solid #f0f0f0;

    &:last-child {
      border-bottom: none;
    }

    .record-time {
      min-width: 160px;
      color: #909399;
      font-size: 14px;
    }

    .record-action {
      min-width: 60px;
      color: #409eff;
      font-weight: 500;
      margin-right: 20px;
    }

    .record-user {
      color: #606266;
    }
  }
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
}

:deep(.el-dialog__body) {
  padding: 20px 24px;
}

:deep(.el-row) {
  margin-bottom: 16px;

  &:last-child {
    margin-bottom: 0;
  }
}
</style>
