<template>
  <div class="page-container">
    <el-card>
      <template #header>
        <div class="card-header">
          <span>空间分布展示</span>
          <div class="header-controls">
            <el-select v-model="selectedPollutant" placeholder="选择污染物">
              <el-option label="PM2.5" value="pm25" />
              <el-option label="PM10" value="pm10" />
              <el-option label="SO2" value="so2" />
              <el-option label="NO2" value="no2" />
            </el-select>
            <el-date-picker
              v-model="selectedDate"
              type="date"
              placeholder="选择日期"
              style="margin-left: 10px;"
            />
          </div>
        </div>
      </template>
      
      <div class="content-area">
        <div class="map-container">
          <div class="map-placeholder">
            <el-icon size="64"><Location /></el-icon>
            <p>地图展示区域</p>
            <p>当前显示: {{ selectedPollutant || 'PM2.5' }} - {{ formatDate(selectedDate) }}</p>
          </div>
        </div>
        
        <div class="legend-container">
          <h4>浓度图例</h4>
          <div class="legend-item" v-for="item in legendData" :key="item.level">
            <div class="legend-color" :style="{ backgroundColor: item.color }"></div>
            <span>{{ item.range }}</span>
            <span class="legend-label">{{ item.label }}</span>
          </div>
        </div>
      </div>
    </el-card>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { Location } from '@element-plus/icons-vue'

const selectedPollutant = ref('pm25')
const selectedDate = ref(new Date())

const legendData = ref([
  { level: 1, color: '#00e400', range: '0-35', label: '优' },
  { level: 2, color: '#ffff00', range: '35-75', label: '良' },
  { level: 3, color: '#ff7e00', range: '75-115', label: '轻度污染' },
  { level: 4, color: '#ff0000', range: '115-150', label: '中度污染' },
  { level: 5, color: '#8f3f97', range: '150-250', label: '重度污染' },
  { level: 6, color: '#7e0023', range: '>250', label: '严重污染' }
])

const formatDate = (date: Date) => {
  if (!date) return '今日'
  return date.toLocaleDateString('zh-CN')
}
</script>

<style scoped>
.page-container {
  padding: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.header-controls {
  display: flex;
  align-items: center;
}

.content-area {
  display: flex;
  gap: 20px;
}

.map-container {
  flex: 1;
  height: 500px;
  border: 1px solid #dcdfe6;
  border-radius: 4px;
}

.map-placeholder {
  height: 100%;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  color: #909399;
  background-color: #f5f7fa;
}

.legend-container {
  width: 200px;
  padding: 20px;
  background-color: #f9f9f9;
  border-radius: 4px;
}

.legend-container h4 {
  margin: 0 0 15px 0;
  color: #303133;
}

.legend-item {
  display: flex;
  align-items: center;
  margin-bottom: 8px;
}

.legend-color {
  width: 20px;
  height: 20px;
  margin-right: 10px;
  border-radius: 2px;
}

.legend-label {
  margin-left: auto;
  font-size: 12px;
  color: #606266;
}
</style>
