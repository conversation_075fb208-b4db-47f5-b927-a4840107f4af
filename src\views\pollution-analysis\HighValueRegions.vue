<template>
  <div class="page-container">
    <el-card>
      <template #header>
        <span>高值区域定位</span>
      </template>
      <div class="regions-content">
        <div class="chart-placeholder">
          <el-icon size="64"><MapLocation /></el-icon>
          <p>高值区域定位分析</p>
        </div>
      </div>
    </el-card>
  </div>
</template>

<script setup lang="ts">
import { MapLocation } from '@element-plus/icons-vue'
</script>

<style scoped>
.page-container {
  padding: 20px;
}
.regions-content {
  height: 400px;
}
.chart-placeholder {
  height: 100%;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  color: #909399;
  background-color: #f5f7fa;
}
</style>
