<template>
  <div class="page-container">
    <el-card>
      <template #header>
        <span>同步环比分析</span>
      </template>
      <div class="analysis-content">
        <div class="chart-placeholder">
          <el-icon size="64"><DataAnalysis /></el-icon>
          <p>同步环比分析图表</p>
        </div>
      </div>
    </el-card>
  </div>
</template>

<script setup lang="ts">
import { DataAnalysis } from '@element-plus/icons-vue'
</script>

<style scoped>
.page-container {
  padding: 20px;
}
.analysis-content {
  height: 400px;
}
.chart-placeholder {
  height: 100%;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  color: #909399;
  background-color: #f5f7fa;
}
</style>
