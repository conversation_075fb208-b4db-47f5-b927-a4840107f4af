<template>
  <div class="layout-container">
    <!-- 顶部导航 -->
    <el-header class="header">
      <div class="header-content">
        <div class="logo">
          <h2>大气服务平台</h2>
        </div>
        <el-menu
          :default-active="activeMenu"
          class="header-menu"
          mode="horizontal"
          @select="handleMenuSelect"
        >
          <template v-for="menu in menuList" :key="menu.index">
            <el-menu-item v-if="menu.children.length === 0" :index="menu.index">
              {{ menu.title }}
            </el-menu-item>
            <el-sub-menu v-else :index="menu.index">
              <template #title>{{ menu.title }}</template>
              <el-menu-item v-for="child in menu.children" :index="child.index">
                {{ child.title }}
              </el-menu-item>
            </el-sub-menu>
          </template>
        </el-menu>
      </div>
    </el-header>

    <!-- 主体内容区域 -->
    <el-container class="main-container">
      <el-aside v-if="showSidebar" class="sidebar" width="200px">
        <el-menu
          :default-active="activeSidebarMenu"
          class="sidebar-menu"
          @select="handleSidebarSelect"
        >
          <div class="sidebar-title">{{ sidebarTitle }}</div>
          <el-menu-item v-for="item in sidebarMenus" :key="item.index" :index="item.index">
            {{ item.title }}
          </el-menu-item>
        </el-menu>
      </el-aside>

      <el-main class="content">
        <router-view />
      </el-main>
    </el-container>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { useMenuStore } from '@/stores/menu'

const router = useRouter()
const route = useRoute()
const menuStore = useMenuStore()

// 从store获取菜单数据
const { menuList, findMenuByRoute, findCategoryByRoute } = menuStore

// 当前激活的菜单
const activeMenu = ref('1')
const activeSidebarMenu = ref('')

// 计算侧边栏相关数据
const showSidebar = computed(() => {
  const currentMenu = findMenuByRoute(route.path)
  return currentMenu && currentMenu.categories && currentMenu.categories.length > 0
})

const sidebarTitle = computed(() => {
  const currentMenu = findMenuByRoute(route.path)
  return currentMenu?.title || ''
})

const sidebarMenus = computed(() => {
  const currentMenu = findMenuByRoute(route.path)
  return currentMenu?.categories || []
})

// 处理顶部菜单选择
const handleMenuSelect = (index: string) => {
  activeMenu.value = index

  if (index === '1') {
    // 首页
    router.push('/')
    activeSidebarMenu.value = ''
  } else {
    // 查找对应的菜单项
    for (const menu of menuList) {
      if (menu.index === index) {
        // 如果是一级菜单，跳转到第一个子菜单
        if (menu.children.length > 0) {
          const firstChild = menu.children[0]
          if (firstChild.route) {
            router.push(firstChild.route)
          }
        }
        break
      }

      // 检查是否是子菜单
      for (const child of menu.children) {
        if (child.index === index && child.route) {
          router.push(child.route)
          break
        }
      }
    }
  }
}

// 处理侧边栏菜单选择
const handleSidebarSelect = (index: string) => {
  activeSidebarMenu.value = index
  const currentMenu = findMenuByRoute(route.path)
  if (currentMenu?.categories) {
    const category = currentMenu.categories.find((cat) => cat.index === index)
    if (category) {
      router.push(category.route)
    }
  }
}

// 监听路由变化，更新菜单状态
watch(
  () => route.path,
  (newPath) => {
    // 根据路由路径更新菜单状态
    const currentMenu = findMenuByRoute(newPath)
    const currentCategory = findCategoryByRoute(newPath)

    if (newPath === '/') {
      activeMenu.value = '1'
      activeSidebarMenu.value = ''
    } else if (currentMenu) {
      // 找到对应的一级菜单
      for (const menu of menuList) {
        for (const child of menu.children) {
          if (child === currentMenu) {
            activeMenu.value = child.index
            break
          }
        }
      }

      // 如果有分类，设置侧边栏选中状态
      if (currentCategory) {
        activeSidebarMenu.value = currentCategory.index
      } else {
        activeSidebarMenu.value = ''
      }
    }
  },
  { immediate: true },
)
</script>

<style scoped>
.layout-container {
  height: 100vh;
  display: flex;
  flex-direction: column;
}

.header {
  background-color: #fff;
  border-bottom: 1px solid #e6e6e6;
  padding: 0;
  height: 60px !important;
}

.header-content {
  display: flex;
  align-items: center;
  height: 100%;
  padding: 0 20px;
}

.logo {
  margin-right: 40px;
}

.logo h2 {
  margin: 0;
  color: #409eff;
  font-size: 20px;
}

.header-menu {
  flex: 1;
  border-bottom: none;
}

.main-container {
  flex: 1;
  height: calc(100vh - 60px);
}

.sidebar {
  background-color: #f5f5f5;
  border-right: 1px solid #e6e6e6;
}

.sidebar-title {
  padding: 15px 20px;
  font-weight: bold;
  color: #303133;
  border-bottom: 1px solid #e6e6e6;
  background-color: #fff;
}

.sidebar-menu {
  border-right: none;
  background-color: #f5f5f5;
}

.content {
  background-color: #f0f2f5;
  padding: 20px;
}

:deep(.el-menu--horizontal .el-menu-item) {
  border-bottom: none;
}

:deep(.el-menu--horizontal .el-sub-menu .el-sub-menu__title) {
  border-bottom: none;
}
</style>
