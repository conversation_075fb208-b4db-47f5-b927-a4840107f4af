import axios from 'axios'
import { config } from '@/config/env'

// 政策信息接口类型定义
export interface PolicyInfo {
  policyName: string
  publishDepartment: string
  publishDate: string
  implementationDate: string
  expiryDate: string
  policyLevel: string
  applicableRegion: string[]
  policyType: string
  policyNumber: string
  coreTheme: string
  controlledPollutants: string[]
  controlTargets: string
  dataStatus: number
  remarks: string
}

export interface PolicyFormData {
  info: PolicyInfo
  fileList: {
    addFileIds: string[]
  }
}

export interface ApiResponse<T = any> {
  code: number
  message: string
  data: T
}

// 创建axios实例
const api = axios.create({
  baseURL: config.baseURL,
  timeout: config.timeout,
  headers: {
    'Content-Type': 'application/json',
  },
})

// 打印当前API配置信息
// console.log('📡 API配置信息:')
// console.log('  环境:', config.env)
// console.log('  基础URL:', config.baseURL)
// console.log('  超时时间:', config.timeout + 'ms')

// 请求拦截器
api.interceptors.request.use(
  (config) => {
    // 添加token
    const token = localStorage.getItem('token')
    if (token) {
      config.headers.Authorization = `Bearer ${token}`
    }
    return config
  },
  (error) => {
    return Promise.reject(error)
  },
)

// 响应拦截器
api.interceptors.response.use(
  (response) => {
    const { data } = response
    if (data.code === 200) {
      return data
    } else {
      return Promise.reject(new Error(data.message || '请求失败'))
    }
  },
  (error) => {
    let message = '网络错误'
    if (error.response) {
      switch (error.response.status) {
        case 401:
          message = '未授权，请重新登录'
          // 可以在这里处理登录跳转
          break
        case 403:
          message = '拒绝访问'
          break
        case 404:
          message = '请求地址出错'
          break
        case 500:
          message = '服务器内部错误'
          break
        default:
          message = error.response.data?.message || '请求失败'
      }
    }
    return Promise.reject(new Error(message))
  },
)

// 政策相关API
export const policyApi = {
  // 保存政策信息
  savePolicyInfo: (data: PolicyFormData): Promise<ApiResponse> => {
    return api.post('/PolicyInfo/savePolicyInfo', data)
  },

  // 更新政策信息
  updatePolicyInfo: (data: PolicyFormData): Promise<ApiResponse> => {
    return api.post('/PolicyInfo/updatePolicyInfo', data)
  },

  // 获取政策列表
  getPolicyList: (params: any): Promise<ApiResponse> => {
    return api.get('/PolicyInfo/getPolicyList', { params })
  },

  // 删除政策
  deletePolicyInfo: (id: string): Promise<ApiResponse> => {
    return api.delete(`/PolicyInfo/deletePolicyInfo/${id}`)
  },

  // 获取政策详情
  getPolicyDetail: (id: string): Promise<ApiResponse> => {
    return api.get(`/PolicyInfo/getPolicyDetail/${id}`)
  },
}

// 文件上传API
export const fileApi = {
  // 上传文件
  uploadFile: (file: File, type: string = 'policyInfo'): Promise<ApiResponse> => {
    const formData = new FormData()
    formData.append('file', file)
    formData.append('type', type)

    return api.post('/File/upload', formData, {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
    })
  },

  // 删除文件
  deleteFile: (fileId: string): Promise<ApiResponse> => {
    return api.delete(`/File/delete/${fileId}`)
  },

  // 下载文件
  downloadFile: (fileId: string): Promise<Blob> => {
    return api.get(`/File/download/${fileId}`, {
      responseType: 'blob',
    })
  },
}

// 字典数据API
export const dictApi = {
  // 获取政策层级字典
  getPolicyLevelDict: (): Promise<ApiResponse> => {
    return api.get('/Dict/getPolicyLevelDict')
  },

  // 获取政策类型字典
  getPolicyTypeDict: (): Promise<ApiResponse> => {
    return api.get('/Dict/getPolicyTypeDict')
  },

  // 获取地区字典
  getRegionDict: (): Promise<ApiResponse> => {
    return api.get('/Dict/getRegionDict')
  },

  // 获取污染物字典
  getPollutantDict: (): Promise<ApiResponse> => {
    return api.get('/Dict/getPollutantDict')
  },
}

export default api
