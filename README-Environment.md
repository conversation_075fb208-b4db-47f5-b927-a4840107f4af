# 环境配置快速指南

## 🌍 环境概览

项目支持三种环境配置，每种环境都有独立的API地址和配置参数：

| 环境 | API地址 | 端口 | 用途 |
|------|---------|------|------|
| 开发环境 | `/api` (代理) | 5174 | 本地开发调试 |
| 测试环境 | `http://127.0.0.1:10054/api` | 5175 | 功能测试 |
| 生产环境 | `https://your-production-domain.com/api` | 5174 | 正式环境 |

## 🚀 快速启动

### 开发环境
```bash
npm run dev
# 访问: http://localhost:5174
```

### 测试环境
```bash
npm run dev:test
# 访问: http://localhost:5175
# API请求将发送到: http://127.0.0.1:10054/api
```

### 生产环境
```bash
npm run dev:prod
# 访问: http://localhost:5174
# API请求将发送到生产环境地址
```

## 📦 构建命令

```bash
# 构建测试环境
npm run build:test

# 构建生产环境
npm run build:prod
```

## 🔧 配置文件

### 环境变量文件
- `.env` - 开发环境默认配置
- `.env.test` - 测试环境配置
- `.env.production` - 生产环境配置

### 核心配置文件
- `src/config/env.ts` - 环境配置管理
- `src/api/policy.ts` - API配置

## 📋 测试页面

访问测试页面查看当前环境配置：
- 开发环境: http://localhost:5174/knowledge/policy-list-test
- 测试环境: http://localhost:5175/knowledge/policy-list-test

## ⚙️ 环境切换

在开发环境下，页面右上角会显示环境切换器，可以：
- 查看当前环境信息
- 快速切换到其他环境
- 刷新页面

## 🔍 环境验证

### 检查当前环境
1. 打开浏览器控制台
2. 查看环境配置信息输出
3. 检查API请求的实际地址

### 测试API连接
1. 访问政策文件库页面
2. 点击"新增政策"按钮
3. 查看网络请求是否发送到正确的API地址

## 📝 注意事项

1. **测试环境API**: 确保 `http://127.0.0.1:10054` 服务正在运行
2. **生产环境配置**: 需要更新 `.env.production` 中的实际生产环境API地址
3. **端口冲突**: 如果端口被占用，Vite会自动选择下一个可用端口
4. **环境变量**: 只有以 `VITE_` 开头的变量才能在前端代码中访问

## 🛠️ 故障排除

### 常见问题
1. **API请求失败**: 检查对应环境的API服务是否启动
2. **环境配置不生效**: 重启开发服务器
3. **端口被占用**: 关闭其他占用端口的服务

### 调试方法
1. 查看浏览器控制台的环境配置输出
2. 检查网络请求的实际地址
3. 使用测试页面验证环境配置
