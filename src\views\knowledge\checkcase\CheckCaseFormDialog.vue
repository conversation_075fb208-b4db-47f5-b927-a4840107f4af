<template>
  <el-dialog
    v-model="dialogVisible"
    :title="isEdit ? '编辑排查案例' : '新增排查案例'"
    width="60%"
    :before-close="handleClose"
    destroy-on-close
  >
    <el-form
      ref="formRef"
      :model="formData"
      :rules="formRules"
      label-width="120px"
      class="form-container"
    >
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="案例名称" prop="checkCaseName">
            <el-input v-model="formData.checkCaseName" placeholder="请输入案例名称" clearable />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="适用地区" prop="applicableRegion">
            <el-cascader
              v-model="formData.applicableRegion"
              placeholder="请选择适用地区"
              :options="applicableRegionOptions"
              filterable
              clearable
              :props="{
                multiple: true,
                value: 'code',
                label: 'name',
                children: 'children',
                // 父子节点不强制关联选中（多选更灵活）
                checkStrictly: true,
                emitPath: false,
              }"
              style="width: 100%"
            />
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="排查开始时间" prop="investigationStartDate">
            <el-date-picker
              v-model="formData.investigationStartDate"
              type="date"
              placeholder="请选择排查开始时间"
              format="YYYY-MM-DD"
              value-format="YYYY-MM-DD"
              style="width: 100%"
            />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="排查结束时间" prop="investigationEndDate">
            <el-date-picker
              v-model="formData.investigationEndDate"
              type="date"
              placeholder="请选择排查结束时间"
              format="YYYY-MM-DD"
              value-format="YYYY-MM-DD"
              style="width: 100%"
            />
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="排查对象" prop="investigationTarget">
            <el-input
              v-model="formData.investigationTarget"
              placeholder="请输入排查对象"
              clearable
            />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="排查来源" prop="source">
            <el-select
              v-model="formData.source"
              placeholder="请选择排查来源"
              multiple
              clearable
              style="width: 100%"
            >
              <el-option
                v-for="item in dictOptions.source"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              />
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="排查类型" prop="investigationType">
            <el-select
              v-model="formData.investigationType"
              placeholder="请选择排查类型"
              multiple
              clearable
              style="width: 100%"
            >
              <el-option
                v-for="item in dictOptions.investigationType"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="关联政策" prop="policyIds">
            <el-select
              v-model="formData.policyIds"
              placeholder="请选择关联政策"
              multiple
              clearable
              style="width: 100%"
            >
              <el-option
                v-for="item in policyOptions"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              />
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="排查方法" prop="investigationMethod">
            <el-input
              v-model="formData.investigationMethod"
              type="textarea"
              :rows="3"
              placeholder="请输入排查方法"
            />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="排查范围" prop="investigationScope">
            <el-input
              v-model="formData.investigationScope"
              type="textarea"
              :rows="3"
              placeholder="请输入排查范围"
            />
          </el-form-item>
        </el-col>
      </el-row>

      <el-form-item label="案例分析总结" prop="caseSummary">
        <el-input
          v-model="formData.caseSummary"
          type="textarea"
          :rows="4"
          placeholder="请输入案例分析总结"
        />
      </el-form-item>

      <el-form-item label="备注" prop="remarks">
        <el-input v-model="formData.remarks" type="textarea" :rows="3" placeholder="请输入备注" />
      </el-form-item>

      <!-- 问题详情 -->
      <el-divider content-position="left">问题详情</el-divider>
      <div class="problem-list">
        <div v-for="(problem, index) in formData.problemList" :key="index" class="problem-item">
          <div class="problem-header">
            <span>问题 {{ index + 1 }}</span>
            <el-button type="danger" size="small" @click="removeProblem(index)"> 删除 </el-button>
          </div>

          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item
                :prop="`problemList.${index}.problemType`"
                label="问题类型"
                :rules="{ required: true, message: '请选择问题类型', trigger: 'change' }"
              >
                <el-select
                  v-model="problem.problemType"
                  placeholder="请选择问题类型"
                  multiple
                  clearable
                  style="width: 100%"
                >
                  <el-option
                    v-for="item in dictOptions.problemType"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value"
                  />
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item
                :prop="`problemList.${index}.rectificationResult`"
                label="整改结果"
                :rules="{ required: true, message: '请选择整改结果', trigger: 'change' }"
              >
                <el-select
                  v-model="problem.rectificationResult"
                  placeholder="请选择整改结果"
                  clearable
                  style="width: 100%"
                >
                  <el-option
                    v-for="item in rectificationResultOptions"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value"
                  />
                </el-select>
              </el-form-item>
            </el-col>
          </el-row>

          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item
                :prop="`problemList.${index}.mainProblemDescription`"
                label="主要问题描述"
                :rules="{ required: true, message: '请输入主要问题描述', trigger: 'blur' }"
              >
                <el-input
                  v-model="problem.mainProblemDescription"
                  type="textarea"
                  :rows="3"
                  placeholder="请输入主要问题描述"
                />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item
                :prop="`problemList.${index}.regulationClause`"
                label="依据法规条款"
                :rules="{ required: true, message: '请输入依据法规条款', trigger: 'blur' }"
              >
                <el-input
                  v-model="problem.regulationClause"
                  type="textarea"
                  :rows="3"
                  placeholder="请输入依据法规条款"
                />
              </el-form-item>
            </el-col>
          </el-row>

          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item
                :prop="`problemList.${index}.measures`"
                label="处理措施"
                :rules="{ required: true, message: '请输入处理措施', trigger: 'blur' }"
              >
                <el-input
                  v-model="problem.measures"
                  type="textarea"
                  :rows="3"
                  placeholder="请输入处理措施"
                />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item
                :prop="`problemList.${index}.rectificationDeadline`"
                label="整改时限"
                :rules="{ required: true, message: '请选择整改时限', trigger: 'change' }"
              >
                <el-date-picker
                  v-model="problem.rectificationDeadline"
                  type="date"
                  placeholder="请选择整改时限"
                  format="YYYY-MM-DD"
                  value-format="YYYY-MM-DD"
                  style="width: 100%"
                />
              </el-form-item>
            </el-col>
          </el-row>

          <el-form-item :prop="`problemList.${index}.remarks`" label="备注">
            <el-input
              v-model="problem.remarks"
              type="textarea"
              :rows="2"
              placeholder="请输入备注"
            />
          </el-form-item>
        </div>

        <el-button type="primary" @click="addProblem">添加问题</el-button>
      </div>

      <!-- 附件上传 -->
      <el-divider content-position="left">附件上传</el-divider>
      <el-form-item label="附件" required>
        <el-upload
          ref="uploadRef"
          :action="uploadAction"
          :headers="uploadHeaders"
          :data="uploadData"
          :file-list="fileList"
          :on-success="handleUploadSuccess"
          :on-error="handleUploadError"
          :on-remove="handleFileRemove"
          :before-upload="beforeUpload"
          multiple
          drag
          class="upload-area"
        >
          <el-icon class="el-icon--upload"><upload-filled /></el-icon>
          <div class="el-upload__text">将文件拖到此处，或<em>点击上传</em></div>
          <template #tip>
            <div class="el-upload__tip">
              支持上传 PDF、DOC、DOCX、XLS、XLSX、PNG、JPG、JPEG 等格式文件，单个文件不超过 10MB
            </div>
          </template>
        </el-upload>
      </el-form-item>
    </el-form>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">取消</el-button>
        <el-button type="info" @click="handleSaveDraft">保存草稿</el-button>
        <el-button type="primary" @click="handleSubmit">提交</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, reactive, watch, onMounted } from 'vue'
import { ElMessage, type FormInstance, type UploadFile } from 'element-plus'
import { UploadFilled } from '@element-plus/icons-vue'
import type { CheckCaseInfo, CheckProblemInfo, CheckCaseFormData } from '@/api/types'
import { checkCaseApi, policyApi } from '@/api/knowledge'
import { fetchDictDataByTypes } from '@/utils/options'
import { getDistrict } from '@/api/common'
import { config } from '@/config/env'

interface Props {
  modelValue: boolean
  formData?: CheckCaseInfo | null
  isEdit?: boolean
}

interface Emits {
  (e: 'update:modelValue', value: boolean): void
  (e: 'success'): void
}

const props = withDefaults(defineProps<Props>(), {
  formData: null,
  isEdit: false,
})

const emit = defineEmits<Emits>()

const dialogVisible = ref(false)
const formRef = ref<FormInstance>()
const loading = ref(false)

// 表单数据
const formData = reactive<CheckCaseInfo>({
  checkCaseName: '',
  applicableRegion: [],
  investigationStartDate: '',
  investigationEndDate: '',
  investigationTarget: '',
  source: [],
  investigationType: [],
  policyIds: [],
  investigationMethod: '',
  investigationScope: '',
  caseSummary: '',
  dataStatus: 2, // 默认暂存
  remarks: '',
  problemList: [],
})

// 文件列表
const fileList = ref<UploadFile[]>([])
const uploadRef = ref()

// 字典数据
const dictOptions = reactive<{ [key: string]: any[] }>({
  source: [],
  investigationType: [],
  problemType: [],
})

// 地区数据
const applicableRegionOptions = ref<any[]>([])

// 政策数据
const policyOptions = ref<any[]>([])

// 整改结果选项
const rectificationResultOptions = ref([
  { value: 1, label: '已完成整改' },
  { value: 2, label: '整改中' },
  { value: 3, label: '未整改' },
])

// 表单验证规则
const formRules = {
  checkCaseName: [{ required: true, message: '请输入案例名称', trigger: 'blur' }],
  applicableRegion: [{ required: true, message: '请选择适用地区', trigger: 'change' }],
  investigationStartDate: [{ required: true, message: '请选择排查开始时间', trigger: 'change' }],
  investigationEndDate: [{ required: true, message: '请选择排查结束时间', trigger: 'change' }],
  investigationTarget: [{ required: true, message: '请输入排查对象', trigger: 'blur' }],
  source: [{ required: true, message: '请选择排查来源', trigger: 'change' }],
  investigationType: [{ required: true, message: '请选择排查类型', trigger: 'change' }],
  investigationMethod: [{ required: true, message: '请输入排查方法', trigger: 'blur' }],
  investigationScope: [{ required: true, message: '请输入排查范围', trigger: 'blur' }],
  caseSummary: [{ required: true, message: '请输入案例分析总结', trigger: 'blur' }],
}
// 初始化表单数据
const initFormData = () => {
  if (props.formData && props.isEdit) {
    Object.assign(formData, props.formData)
    // 处理文件列表
    if (props.formData.fileList) {
      fileList.value = props.formData.fileList.map((file: any) => ({
        uid: file.fileId,
        name: file.fileName,
        status: 'success',
        response: { fileId: file.fileId },
        url: file.url,
      }))
    }
  } else {
    // 重置表单
    Object.assign(formData, {
      checkCaseName: '',
      applicableRegion: [],
      investigationStartDate: '',
      investigationEndDate: '',
      investigationTarget: '',
      source: [],
      investigationType: [],
      policyIds: [],
      investigationMethod: '',
      investigationScope: '',
      caseSummary: '',
      dataStatus: 2,
      remarks: '',
      problemList: [],
    })
    fileList.value = []
  }

  // 如果没有问题，添加一个默认问题
  if (formData.problemList.length === 0) {
    addProblem()
  }
}
// 监听弹窗显示状态
watch(
  () => props.modelValue,
  (val) => {
    dialogVisible.value = val
    if (val) {
      initFormData()
    }
  },
  { immediate: true },
)

watch(dialogVisible, (val) => {
  emit('update:modelValue', val)
})

// 添加问题
const addProblem = () => {
  const newProblem: CheckProblemInfo = {
    problemType: [],
    mainProblemDescription: '',
    regulationClause: '',
    measures: '',
    rectificationDeadline: '',
    rectificationResult: '',
    remarks: '',
  }
  formData.problemList.push(newProblem)
}

// 删除问题
const removeProblem = (index: number) => {
  if (formData.problemList.length > 1) {
    formData.problemList.splice(index, 1)
  } else {
    ElMessage.warning('至少需要保留一个问题')
  }
}

// 初始化字典数据
const initDictData = async () => {
  try {
    const dictTypeMap = {
      source: 'pcly',
      investigationType: 'pclx',
      problemType: 'pcwtlx',
    }
    const dictData = await fetchDictDataByTypes(dictTypeMap)
    Object.assign(dictOptions, dictData)
  } catch (error) {
    console.error('获取字典数据失败:', error)
  }
}

// 获取地区数据
const fetchApplicableRegion = async () => {
  try {
    const res = await getDistrict({ code: '' })
    applicableRegionOptions.value = res
  } catch (error) {
    console.error('获取地区数据失败:', error)
  }
}

// 获取政策数据
const fetchPolicyData = async () => {
  try {
    const res = await policyApi.getPolicyInfoList({
      currentPage: 1,
      pageSize: 1000,
      policyName: '',
      publishStartDate: '',
      publishEndDate: '',
      policyLevel: '',
      applicableRegion: '',
      policyType: '',
      policyNumber: '',
      controlledPollutants: '',
      dataStatus: '1', // 只获取正常状态的政策
    })
    policyOptions.value = res.list.map((item: any) => ({
      value: item.policyId,
      label: item.policyName,
    }))
  } catch (error) {
    console.error('获取政策数据失败:', error)
  }
}

// 保存草稿
const handleSaveDraft = async () => {
  try {
    await formRef.value?.validate()
    await submitForm(2) // 暂存状态
  } catch (error) {
    console.error('表单验证失败:', error)
  }
}

// 提交
const handleSubmit = async () => {
  try {
    await formRef.value?.validate()
    await submitForm(3) // 提交状态
  } catch (error) {
    console.error('表单验证失败:', error)
  }
}

// 提交表单
const submitForm = async (dataStatus: number) => {
  loading.value = true
  try {
    // 收集文件ID
    const addFileIds = fileList.value
      .filter((file) => file.response && (file.response as { fileId: string }).fileId)
      .map((file) => (file.response as { fileId: string }).fileId)
    if (addFileIds.length === 0) {
      ElMessage.error('请至少上传一个附件')
      return
    }
    const submitData: CheckCaseFormData = {
      info: {
        ...formData,
        dataStatus,
      },
      fileList: {
        addFileIds,
        delFileIds,
      },
    }

    if (props.isEdit) {
      await checkCaseApi.updateCheckCaseInfo(submitData)
      ElMessage.success('更新成功')
    } else {
      await checkCaseApi.saveCheckCaseInfo(submitData)
      ElMessage.success('保存成功')
    }

    emit('success')
  } catch (error) {
    console.error('提交失败:', error)
    ElMessage.error('提交失败')
  } finally {
    loading.value = false
  }
}

// 关闭弹窗
const handleClose = () => {
  dialogVisible.value = false
}

// 文件上传配置
const uploadAction = config.baseURL + 'File/upload'
const uploadHeaders = {
  Authorization: 'Bearer ' + (localStorage.getItem('token') || ''),
}
const uploadData = {
  type: 'checkCaseInfo',
}

// 文件上传相关方法
const beforeUpload = (file: File) => {
  const allowedTypes = [
    // 图片
    'image/jpeg',
    'image/png',
    'image/gif',
    // PDF、DOC、DOCX、XLS、XLSX
    'application/pdf',
    'application/msword',
    'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
    'application/vnd.ms-excel',
    'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
  ]

  const isAllowedType = allowedTypes.includes(file.type)
  const isLt10M = file.size / 1024 / 1024 < 10

  if (!isAllowedType) {
    ElMessage.error('只能上传 PDF、DOC、DOCX、XLS、XLSX、PNG、JPG、JPEG 格式的文件!')
    return false
  }
  if (!isLt10M) {
    ElMessage.error('上传文件大小不能超过 10MB!')
    return false
  }
  return true
}

const handleUploadSuccess = (response: any, file: UploadFile, uploadFileList: UploadFile[]) => {
  if (response.code === 200) {
    file.response = response.data
    fileList.value = uploadFileList
    ElMessage.success('文件上传成功')
  } else {
    ElMessage.error(response.message || '文件上传失败')
    const index = uploadFileList.findIndex((f: UploadFile) => f.uid === file.uid)
    if (index > -1) {
      uploadFileList.splice(index, 1)
    }
  }
}

const handleUploadError = (error: any) => {
  ElMessage.error('文件上传失败: ' + error.message)
}
const delFileIds: string[] = []
const handleFileRemove = (file: UploadFile, uploadFileList: UploadFile[]) => {
  // 从文件列表中移除
  // 将删除的id添加到delFileIds中
  if (file.response && typeof (file.response as any).fileId !== 'undefined') {
    delFileIds.push(String((file.response as any).fileId))
  }
  fileList.value = uploadFileList
}

// 初始化
onMounted(() => {
  initDictData()
  fetchApplicableRegion()
  fetchPolicyData()
})
</script>

<style scoped>
.form-container {
  max-height: 70vh;
  padding: 0 30px;
  overflow-y: auto;
}

.problem-list {
  border: 1px solid #e4e7ed;
  border-radius: 4px;
  padding: 20px;
  margin-bottom: 20px;
}

.problem-item {
  border: 1px solid #f0f0f0;
  border-radius: 4px;
  padding: 20px;
  margin-bottom: 20px;
  background-color: #fafafa;
}

.problem-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  font-weight: bold;
}

.dialog-footer {
  text-align: right;
}

.upload-area {
  width: 100%;
}

.upload-area .el-upload {
  width: 100%;
}

.upload-area .el-upload-dragger {
  width: 100%;
}
</style>
