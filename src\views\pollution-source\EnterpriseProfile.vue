<template>
  <div class="page-container">
    <el-card>
      <template #header>
        <span>企一档</span>
      </template>
      <div class="profile-content">
        <div class="chart-placeholder">
          <el-icon size="64"><Document /></el-icon>
          <p>企业档案管理</p>
        </div>
      </div>
    </el-card>
  </div>
</template>

<script setup lang="ts">
import { Document } from '@element-plus/icons-vue'
</script>

<style scoped>
.page-container {
  padding: 20px;
}
.profile-content {
  height: 400px;
}
.chart-placeholder {
  height: 100%;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  color: #909399;
  background-color: #f5f7fa;
}
</style>
