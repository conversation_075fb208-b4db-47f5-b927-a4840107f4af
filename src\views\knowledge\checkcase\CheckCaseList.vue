<template>
  <div class="page-container">
    <!-- 搜索区域 -->
    <CollapsePanel title="搜索条件" :show-toggle-btn="false" :expended="true">
      <template #panel-button>
        <el-button size="small" type="primary" @click="handleSearch">搜索</el-button>
        <el-button size="small" @click="handleReset">重置</el-button>
      </template>
      <template #panel-main>
        <!-- 搜索表单 -->
        <SearchForm ref="searchFormRef" :config="searchConfig" :model="searchParams" />
      </template>
    </CollapsePanel>
    <CollapsePanel
      :title="`共找到${pagination.total}条记录`"
      :show-toggle-btn="false"
      :expended="true"
    >
      <template #panel-button>
        <el-button type="primary" :icon="Plus" @click="handleAdd" size="small">新增</el-button>
      </template>
      <template #panel-main>
        <div class="lsit-container">
          <el-table
            v-loading="loading"
            :data="tableData"
            style="width: 100%"
            @selection-change="handleSelectionChange"
          >
            <!-- <el-table-column type="selection" width="55" /> -->
            <el-table-column
              prop="checkCaseName"
              label="案例名称"
              min-width="200"
              show-overflow-tooltip
            />
            <el-table-column
              prop="applicableRegionName"
              label="适用地区"
              min-width="120"
              show-overflow-tooltip
            />
            <el-table-column label="排查时间" min-width="180">
              <template #default="{ row }">
                {{ row.investigationStartDate }} ~ {{ row.investigationEndDate }}
              </template>
            </el-table-column>
            <el-table-column
              prop="investigationTarget"
              label="排查对象"
              min-width="120"
              show-overflow-tooltip
            />
            <el-table-column
              prop="sourceName"
              label="排查来源"
              min-width="120"
              show-overflow-tooltip
            />
            <el-table-column
              prop="investigationTypeName"
              label="排查类型"
              min-width="120"
              show-overflow-tooltip
            />
            <el-table-column
              prop="problemTypeName"
              label="问题类型"
              min-width="150"
              show-overflow-tooltip
            />
            <el-table-column prop="dataStatusName" label="状态" min-width="100">
              <template #default="{ row }">
                <el-tag :type="getStatusTagType(row.dataStatus)" size="small">
                  {{ row.dataStatusName }}
                </el-tag>
              </template>
            </el-table-column>
            <el-table-column prop="updateTime" label="更新时间" min-width="160" />
            <el-table-column label="操作" width="260" fixed="right">
              <template #default="{ row }">
                <el-button size="small" type="primary" link @click="handleView(row)"
                  >查看</el-button
                >
                <el-button size="small" type="primary" link @click="handleEdit(row)"
                  >编辑</el-button
                >
                <el-button
                  v-if="row.dataStatus === 3"
                  size="small"
                  type="success"
                  link
                  @click="handleApprove(row, 1)"
                >
                  通过
                </el-button>
                <el-button
                  v-if="row.dataStatus === 3"
                  size="small"
                  type="warning"
                  link
                  @click="handleApprove(row, -3)"
                >
                  不通过
                </el-button>
                <el-button size="small" type="danger" link @click="handleDelete(row)"
                  >删除</el-button
                >
              </template>
            </el-table-column>
          </el-table>

          <!-- 分页 -->
          <div class="pagination-container">
            <el-pagination
              v-model:current-page="pagination.currentPage"
              v-model:page-size="pagination.pageSize"
              :total="pagination.total"
              :page-sizes="[10, 20, 50, 100]"
              layout="total, sizes, prev, pager, next, jumper"
              @size-change="handleSizeChange"
              @current-change="handleCurrentChange"
            />
          </div>
        </div>
      </template>
    </CollapsePanel>

    <!-- 表单弹窗 -->
    <CheckCaseFormDialog
      v-model="formDialogVisible"
      :form-data="currentFormData"
      :is-edit="isEdit"
      @success="handleFormSuccess"
    />

    <!-- 详情弹窗 -->
    <CheckCaseDetailDialog v-model="detailDialogVisible" :detail-data="currentDetailData" />
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Plus } from '@element-plus/icons-vue'
import CollapsePanel from '@/components/CollapsePanel.vue'
import SearchForm from '@/components/SearchForm.vue'
import type { FormItem } from '@/components/SearchForm.vue'
import type { CheckCaseInfo, CheckCaseSearchParams } from '@/api/types'
import { checkCaseApi } from '@/api/knowledge'
import { fetchDictDataByTypes } from '@/utils/options'
import { getDistrict } from '@/api/common'
import CheckCaseFormDialog from './CheckCaseFormDialog.vue'
import CheckCaseDetailDialog from './CheckCaseDetailDialog.vue'
const searchFormRef = ref()
// 搜索参数
const searchParams = ref<CheckCaseSearchParams>({
  currentPage: 1,
  pageSize: 20,
  checkCaseName: '',
  applicableRegion: '',
  investigationStartDate: '',
  investigationEndDate: '',
  investigationTarget: '',
  source: '',
  investigationType: '',
  problemType: [],
  dataStatus: '',
})

// 分页信息
const pagination = reactive({
  currentPage: 1,
  pageSize: 20,
  total: 0,
})

// 表格数据
const tableData = ref<CheckCaseInfo[]>([])
const loading = ref(false)
const selectedRows = ref<CheckCaseInfo[]>([])

// 弹窗控制
const formDialogVisible = ref(false)
const detailDialogVisible = ref(false)
const isEdit = ref(false)
const currentFormData = ref<CheckCaseInfo | null>(null)
const currentDetailData = ref<CheckCaseInfo | null>(null)

// 字典数据
const dictOptions = reactive<{ [key: string]: any[] }>({
  source: [],
  investigationType: [],
  problemType: [],
  dataStatus: [],
})

// 地区数据
const applicableRegionOptions = ref<any[]>([])

// 搜索表单配置
const searchConfig = ref<FormItem[]>([
  {
    type: 'input',
    prop: 'checkCaseName',
    formItem: { label: '案例名称' },
    attrs: {
      placeholder: '请输入案例名称',
      clearable: true,
    },
  },
  {
    type: 'cascader',
    prop: 'applicableRegion',
    formItem: { label: '适用地区' },
    attrs: {
      placeholder: '请选择适用地区',
      options: applicableRegionOptions.value,
      filterable: true,
      clearable: true,
      props: {
        // multiple: true,
        value: 'code',
        label: 'name',
        children: 'children',
        // 父子节点不强制关联选中（多选更灵活）
        checkStrictly: true,
        emitPath: false,
      },
    },
  },
  {
    type: 'daterange',
    prop: 'investigationDateRange',
    formItem: { label: '排查时间' },
    attrs: {
      clearable: true,
      format: 'YYYY-MM-DD',
      valueFormat: 'YYYY-MM-DD',
    },
  },
  {
    type: 'input',
    prop: 'investigationTarget',
    formItem: { label: '排查对象' },
    attrs: { placeholder: '请输入排查对象', clearable: true },
  },
  {
    type: 'select',
    prop: 'source',
    formItem: { label: '排查来源' },
    attrs: { placeholder: '请选择排查来源', clearable: true, options: [] },
  },
  {
    type: 'select',
    prop: 'investigationType',
    formItem: { label: '排查类型' },
    attrs: { placeholder: '请选择排查类型', clearable: true, options: [] },
  },
  {
    type: 'select',
    prop: 'problemType',
    formItem: { label: '问题类型' },
    attrs: { placeholder: '请选择问题类型', clearable: true, multiple: true, options: [] },
  },
  {
    type: 'select',
    prop: 'dataStatus',
    formItem: { label: '状态' },
    attrs: { placeholder: '请选择状态', clearable: true, options: [] },
  },
])

// 初始化字典数据
const initDictData = async () => {
  try {
    const dictTypeMap = {
      source: 'pcly',
      investigationType: 'pclx',
      problemType: 'pcwtlx',
    }
    const dictData = await fetchDictDataByTypes(dictTypeMap)

    // 获取状态下拉
    const statusRes = await checkCaseApi.getDataStatusList()
    dictData.dataStatus = statusRes || []

    // 更新搜索配置中的选项
    searchConfig.value.forEach((item) => {
      if (item.type === 'select' && dictData[item.prop]) {
        if (!item.attrs) item.attrs = {}
        item.attrs.options = dictData[item.prop]
      }
    })

    Object.assign(dictOptions, dictData)
  } catch (error) {
    console.error('获取字典数据失败:', error)
  }
}

// 获取地区数据
const fetchApplicableRegion = async () => {
  try {
    const res = await getDistrict({ code: '' })
    applicableRegionOptions.value = res

    // 更新搜索配置中的地区选项
    const regionConfig = searchConfig.value.find((item) => item.prop === 'applicableRegion')
    if (regionConfig && regionConfig.attrs) {
      regionConfig.attrs.options = res
    }
  } catch (error) {
    console.error('获取地区数据失败:', error)
  }
}

// 获取状态标签类型
const getStatusTagType = (status: number) => {
  switch (status) {
    case 1:
      return 'success' // 正常
    case 2:
      return 'info' // 暂存
    case 3:
      return 'warning' // 待审核
    case -1:
      return 'danger' // 失效
    case -3:
      return 'danger' // 不通过
    default:
      return 'info'
  }
}

// 获取列表数据
const getList = async () => {
  loading.value = true
  try {
    console.log('%c Line:347 🍿 searchParams.value', 'color:#465975', searchParams.value)
    const params = { ...searchParams.value }
    params.currentPage = pagination.currentPage
    params.pageSize = pagination.pageSize
    const res = await checkCaseApi.getCheckCaseInfoList(params)
    tableData.value = res.list || []
    pagination.total = res.pagination?.total || 0
  } catch (error) {
    console.error('获取列表失败:', error)
    ElMessage.error('获取列表失败')
  } finally {
    loading.value = false
  }
}

// 搜索
const handleSearch = () => {
  const formData = searchFormRef.value?.getFormData()
  console.log('%c Line:358 🌭 formData', 'color:#ffdd4d', formData)
  Object.assign(searchParams.value, formData)
  // 处理日期范围
  if (formData) {
    searchParams.value.investigationStartDate = formData.investigationDateRange[0]
    searchParams.value.investigationEndDate = formData.investigationDateRange[1]
  } else {
    searchParams.value.investigationStartDate = ''
    searchParams.value.investigationEndDate = ''
  }

  pagination.currentPage = 1
  getList()
}

// 重置搜索
const handleReset = () => {
  Object.assign(searchParams.value, {
    currentPage: 1,
    pageSize: 20,
    checkCaseName: '',
    applicableRegion: '',
    investigationStartDate: '',
    investigationEndDate: '',
    investigationTarget: '',
    source: '',
    investigationType: '',
    problemType: [],
    dataStatus: 1,
  })
  pagination.currentPage = 1
  getList()
}

// 分页处理
const handleSizeChange = (size: number) => {
  pagination.pageSize = size
  pagination.currentPage = 1
  getList()
}

const handleCurrentChange = (page: number) => {
  pagination.currentPage = page
  getList()
}

// 表格选择
const handleSelectionChange = (selection: CheckCaseInfo[]) => {
  selectedRows.value = selection
}

// 新增
const handleAdd = () => {
  currentFormData.value = null
  isEdit.value = false
  formDialogVisible.value = true
}

// 编辑
const handleEdit = (row: CheckCaseInfo) => {
  currentFormData.value = { ...row }
  isEdit.value = true
  formDialogVisible.value = true
}

// 查看详情
const handleView = (row: CheckCaseInfo) => {
  currentDetailData.value = row
  detailDialogVisible.value = true
}

// 审核
const handleApprove = async (row: CheckCaseInfo, status: number) => {
  const action = status === 1 ? '通过' : '不通过'
  try {
    await ElMessageBox.confirm(`确定要${action}该案例吗？`, '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning',
    })

    await checkCaseApi.approvedCheckCaseInfo({
      info: {
        checkCaseId: row.checkCaseId!,
        dataStatus: status,
      },
    })

    ElMessage.success(`${action}成功`)
    getList()
  } catch (error) {
    if (error !== 'cancel') {
      console.error(`${action}失败:`, error)
      ElMessage.error(`${action}失败`)
    }
  }
}

// 删除
const handleDelete = async (row: CheckCaseInfo) => {
  try {
    await ElMessageBox.confirm('确定要删除该案例吗？', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning',
    })

    await checkCaseApi.deleteCheckCaseInfo({
      info: { checkCaseId: row.checkCaseId! },
    })

    ElMessage.success('删除成功')
    getList()
  } catch (error) {
    if (error !== 'cancel') {
      console.error('删除失败:', error)
      ElMessage.error('删除失败')
    }
  }
}

// 表单提交成功
const handleFormSuccess = () => {
  formDialogVisible.value = false
  getList()
}

// 初始化
onMounted(() => {
  initDictData()
  fetchApplicableRegion()
  getList()
})
</script>

<style scoped>
.table-card {
  margin-top: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.header-buttons {
  display: flex;
  gap: 10px;
}

.pagination-container {
  padding: 20px 0;
  display: flex;
  justify-content: center;
}
.list-container {
  margin-top: 20px;
}
</style>
