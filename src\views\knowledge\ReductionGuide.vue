<template>
  <div class="page-container">
    <el-card>
      <template #header>
        <div class="card-header">
          <span>总量减排核算指南库</span>
          <el-button type="primary">添加指南</el-button>
        </div>
      </template>
      
      <el-table :data="tableData" style="width: 100%">
        <el-table-column prop="title" label="指南名称" width="300" />
        <el-table-column prop="pollutant" label="污染物类型" width="150" />
        <el-table-column prop="industry" label="适用行业" width="150" />
        <el-table-column prop="version" label="版本" width="100" />
        <el-table-column prop="updateTime" label="更新时间" width="180" />
        <el-table-column label="操作" width="200">
          <template #default="scope">
            <el-button size="small" @click="handleView(scope.row)">查看</el-button>
            <el-button size="small" type="primary" @click="handleEdit(scope.row)">编辑</el-button>
            <el-button size="small" type="danger" @click="handleDelete(scope.row)">删除</el-button>
          </template>
        </el-table-column>
      </el-table>
    </el-card>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'

const tableData = ref([
  {
    id: 1,
    title: '工业源SO2减排核算方法',
    pollutant: '二氧化硫',
    industry: '电力行业',
    version: 'v2.1',
    updateTime: '2024-01-20'
  },
  {
    id: 2,
    title: 'NOx减排量计算指南',
    pollutant: '氮氧化物',
    industry: '钢铁行业',
    version: 'v1.5',
    updateTime: '2024-01-18'
  }
])

const handleView = (row: any) => {
  console.log('查看指南:', row)
}

const handleEdit = (row: any) => {
  console.log('编辑指南:', row)
}

const handleDelete = (row: any) => {
  console.log('删除指南:', row)
}
</script>

<style scoped>
.page-container {
  padding: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}
</style>
