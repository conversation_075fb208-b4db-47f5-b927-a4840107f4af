import { getDict, getDistrict } from '@/api/common'

// 全局地区数据缓存
let globalDistrictData: any[] = []

// 处理地区数据格式
const processDistrictData = (data: any[]): any[] => {
  return data.map((item) => ({
    label: item.name,
    value: item.code,
    type: item.value,
    children: item.children ? processDistrictData(item.children) : undefined,
  }))
}

// 获取地区数据（支持全局缓存）
export const fetchDistrictData = async (): Promise<any[]> => {
  if (globalDistrictData.length > 0) {
    return globalDistrictData
  }

  try {
    const res = await getDistrict({ code: '' })
    globalDistrictData = processDistrictData(res)
    return globalDistrictData
  } catch (error) {
    console.error('获取地区数据失败:', error)
    return []
  }
}

// 公共字典数据获取方法
export const fetchDictDataByTypes = async (dictTypeMap: Record<string, string>) => {
  const result: Record<string, any[]> = {}
  try {
    // 并行请求所有字典类型数据
    const promises = Object.entries(dictTypeMap).map(async ([key, type]) => {
      const res = await getDict({ type })
      // 删除第一项
      res.shift()
      result[key] = res.length > 0 ? res : []
    })
    // 等待所有请求完成
    await Promise.all(promises)
    return result
  } catch (error) {
    console.error('获取字典数据失败:', error)
    return result
  }
}

// 获取单个字典数据
export const fetchDictData = async (type: string): Promise<any[]> => {
  try {
    const res = await getDict({ type })
    // 删除第一项
    res.shift()
    return res.length > 0 ? res : []
  } catch (error) {
    console.error(`获取字典数据失败 (${type}):`, error)
    return []
  }
}
