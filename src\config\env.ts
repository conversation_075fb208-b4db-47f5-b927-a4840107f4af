// 环境配置
export interface EnvConfig {
  baseURL: string
  timeout: number
  env: 'development' | 'production'
}

// 获取当前环境
const getEnv = (): 'development' | 'production' => {
  // 可以通过环境变量或其他方式判断环境
  const env = import.meta.env.MODE

  if (env === 'production') {
    return 'production'
  } else {
    return 'development'
  }
}

// 环境配置
const envConfigs: Record<string, EnvConfig> = {
  // 开发环境
  development: {
    baseURL: import.meta.env.VITE_API_BASE_URL || '/api',
    timeout: Number(import.meta.env.VITE_API_TIMEOUT) || 10000,
    env: 'development',
  },

  // 生产环境（待配置）
  production: {
    baseURL: import.meta.env.VITE_API_BASE_URL || 'https://your-production-domain.com/api',
    timeout: Number(import.meta.env.VITE_API_TIMEOUT) || 20000,
    env: 'production',
  },
}

// 获取当前环境配置
export const getCurrentEnvConfig = (): EnvConfig => {
  const currentEnv = getEnv()
  return envConfigs[currentEnv]
}

// 导出当前配置
export const config = getCurrentEnvConfig()

// 导出环境判断函数
export const isDevelopment = () => config.env === 'development'
export const isProduction = () => config.env === 'production'

// 打印当前环境信息（仅在开发环境）
if (isDevelopment()) {
  // console.log('🚀 当前环境:', config.env)
  // console.log('🌐 API地址:', config.baseURL)
}
