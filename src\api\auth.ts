import { get, post } from '@/utils/request'
import type { LoginParams, LoginResult, User, ApiResponse } from './types'

// 用户登录
export const login = (data: LoginParams): Promise<LoginResult> => {
  return post<LoginResult>('/auth/login', data)
}

// 用户登出
export const logout = (): Promise<void> => {
  return post<void>('/auth/logout')
}

// 获取用户信息
export const getUserInfo = (): Promise<User> => {
  return get<User>('/auth/userinfo')
}

// 刷新token
export const refreshToken = (refreshToken: string): Promise<LoginResult> => {
  return post<LoginResult>('/auth/refresh', { refreshToken })
}

// 修改密码
export const changePassword = (data: {
  oldPassword: string
  newPassword: string
  confirmPassword: string
}): Promise<void> => {
  return post<void>('/auth/change-password', data)
}

// 获取验证码
export const getCaptcha = (): Promise<{
  captchaId: string
  captchaImage: string
}> => {
  return get('/auth/captcha')
}

// 忘记密码
export const forgotPassword = (email: string): Promise<void> => {
  return post<void>('/auth/forgot-password', { email })
}

// 重置密码
export const resetPassword = (data: {
  token: string
  newPassword: string
  confirmPassword: string
}): Promise<void> => {
  return post<void>('/auth/reset-password', data)
}

// 检查用户名是否存在
export const checkUsername = (username: string): Promise<boolean> => {
  return get<boolean>('/auth/check-username', { username })
}

// 用户注册
export const register = (data: {
  username: string
  password: string
  email: string
  phone?: string
  captcha: string
  captchaId: string
}): Promise<void> => {
  return post<void>('/auth/register', data)
}
