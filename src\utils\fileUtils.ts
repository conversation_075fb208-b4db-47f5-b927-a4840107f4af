import { ElMessage } from 'element-plus'

// 支持的文件类型
export const SUPPORTED_FILE_TYPES = {
  PDF: 'application/pdf',
  DOC: 'application/msword',
  DOCX: 'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
  XLS: 'application/vnd.ms-excel',
  XLSX: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
  // 图片类型
  JPEG: 'image/jpeg',
  JPG: 'image/jpeg',
  PNG: 'image/png',
  GIF: 'image/gif',
  BMP: 'image/bmp',
  WEBP: 'image/webp',
  SVG: 'image/svg+xml',
}

// 支持的文件扩展名
export const SUPPORTED_EXTENSIONS = [
  '.pdf',
  '.doc',
  '.docx',
  '.xls',
  '.xlsx',
  '.jpg',
  '.jpeg',
  '.png',
  '.gif',
  '.bmp',
  '.webp',
  '.svg',
]

// 图片文件扩展名
export const IMAGE_EXTENSIONS = ['.jpg', '.jpeg', '.png', '.gif', '.bmp', '.webp', '.svg']

// PDF文件扩展名
export const PDF_EXTENSIONS = ['.pdf']

// 文件信息接口
export interface FileInfo {
  fileId: string
  fileName: string
  filePath?: string
  url: string
  fileType?: string
  size?: number
}

/**
 * 获取文件扩展名
 * @param fileName 文件名
 * @returns 文件扩展名（小写）
 */
export const getFileExtension = (fileName: string): string => {
  const lastDotIndex = fileName.lastIndexOf('.')
  return lastDotIndex !== -1 ? fileName.substring(lastDotIndex).toLowerCase() : ''
}

/**
 * 检查文件类型是否支持
 * @param fileName 文件名
 * @returns 是否支持
 */
export const isSupportedFileType = (fileName: string): boolean => {
  const extension = getFileExtension(fileName)
  return SUPPORTED_EXTENSIONS.includes(extension)
}

/**
 * 获取文件类型描述
 * @param fileName 文件名
 * @returns 文件类型描述
 */
export const getFileTypeDescription = (fileName: string): string => {
  const extension = getFileExtension(fileName).toLowerCase()
  switch (extension) {
    case '.pdf':
      return 'PDF文档'
    case '.doc':
      return 'Word文档'
    case '.docx':
      return 'Word文档'
    case '.xls':
      return 'Excel表格'
    case '.xlsx':
      return 'Excel表格'
    case '.jpg':
    case '.jpeg':
      return 'JPEG图片'
    case '.png':
      return 'PNG图片'
    case '.gif':
      return 'GIF图片'
    case '.bmp':
      return 'BMP图片'
    case '.webp':
      return 'WebP图片'
    case '.svg':
      return 'SVG图片'
    default:
      return '未知文件'
  }
}

/**
 * 检查是否为图片文件
 * @param fileName 文件名
 * @returns 是否为图片
 */
export const isImageFile = (fileName: string): boolean => {
  const extension = getFileExtension(fileName)
  return IMAGE_EXTENSIONS.includes(extension)
}

/**
 * 检查是否为PDF文件
 * @param fileName 文件名
 * @returns 是否为PDF
 */
export const isPDFFile = (fileName: string): boolean => {
  const extension = getFileExtension(fileName)
  return PDF_EXTENSIONS.includes(extension)
}

/**
 * 格式化文件大小
 * @param bytes 字节数
 * @returns 格式化后的文件大小
 */
export const formatFileSize = (bytes: number): string => {
  if (bytes === 0) return '0 B'

  const k = 1024
  const sizes = ['B', 'KB', 'MB', 'GB']
  const i = Math.floor(Math.log(bytes) / Math.log(k))

  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
}

/**
 * 从blob URL获取文件数据
 * @param blobUrl blob格式的文件地址
 * @returns Promise<Blob>
 */
export const fetchBlobFromUrl = async (blobUrl: string): Promise<Blob> => {
  try {
    const response = await fetch(blobUrl)
    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`)
    }
    return await response.blob()
  } catch (error) {
    console.error('获取blob数据失败:', error)
    throw error
  }
}

/**
 * 创建对象URL
 * @param blob Blob对象
 * @returns 对象URL
 */
export const createObjectURL = (blob: Blob): string => {
  return URL.createObjectURL(blob)
}

/**
 * 释放对象URL
 * @param objectUrl 对象URL
 */
export const revokeObjectURL = (objectUrl: string): void => {
  URL.revokeObjectURL(objectUrl)
}

/**
 * 图片预览功能
 * @param file 文件信息
 * @param options 预览选项
 */
export const previewImage = async (
  file: FileInfo,
  options?: {
    newWindow?: boolean
    width?: number
    height?: number
  },
): Promise<void> => {
  try {
    if (!file.url) {
      ElMessage.warning('图片预览链接不可用')
      return
    }

    if (!isImageFile(file.fileName)) {
      ElMessage.warning('该文件不是图片格式')
      return
    }

    const { newWindow = true, width = 800, height = 600 } = options || {}

    // 获取blob数据并创建对象URL
    const blob = await fetchBlobFromUrl(file.url)
    const objectUrl = createObjectURL(blob)

    if (newWindow) {
      // 在新窗口中打开图片
      const previewWindow = window.open(
        '',
        '_blank',
        `width=${width},height=${height},scrollbars=yes,resizable=yes`,
      )

      if (!previewWindow) {
        ElMessage.error('无法打开预览窗口，请检查浏览器弹窗设置')
        revokeObjectURL(objectUrl)
        return
      }

      previewWindow.document.write(`
        <!DOCTYPE html>
        <html>
        <head>
          <title>图片预览 - ${file.fileName}</title>
          <style>
            body {
              margin: 0;
              padding: 20px;
              background: #f5f5f5;
              display: flex;
              justify-content: center;
              align-items: center;
              min-height: 100vh;
              font-family: Arial, sans-serif;
            }
            .container {
              text-align: center;
              background: white;
              padding: 20px;
              border-radius: 8px;
              box-shadow: 0 2px 10px rgba(0,0,0,0.1);
              max-width: 90%;
              max-height: 90%;
            }
            .title {
              margin-bottom: 20px;
              color: #333;
              font-size: 18px;
              font-weight: bold;
            }
            img {
              max-width: 100%;
              max-height: 70vh;
              object-fit: contain;
              border-radius: 4px;
              box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            }
            .actions {
              margin-top: 20px;
            }
            .btn {
              background: #409eff;
              color: white;
              border: none;
              padding: 8px 16px;
              border-radius: 4px;
              cursor: pointer;
              margin: 0 5px;
              font-size: 14px;
            }
            .btn:hover {
              background: #337ecc;
            }
          </style>
        </head>
        <body>
          <div class="container">
            <div class="title">${file.fileName}</div>
            <img src="${objectUrl}" alt="${file.fileName}" onload="window.focus()" />
            <div class="actions">
              <button class="btn" onclick="window.close()">关闭</button>
            </div>
          </div>
          <script>
            // 窗口关闭时释放对象URL
            window.addEventListener('beforeunload', function() {
              URL.revokeObjectURL('${objectUrl}');
            });
          </script>
        </body>
        </html>
      `)
      previewWindow.document.close()
    } else {
      // 在当前窗口中打开
      window.location.href = objectUrl
    }

    ElMessage.success('正在打开图片预览...')
  } catch (error) {
    console.error('图片预览失败:', error)
    ElMessage.error('图片预览失败，请稍后重试')
  }
}

/**
 * PDF预览功能
 * @param file 文件信息
 * @param options 预览选项
 */
export const previewPDF = async (
  file: FileInfo,
  options?: {
    newWindow?: boolean
    width?: number
    height?: number
  },
): Promise<void> => {
  try {
    if (!file.url) {
      ElMessage.warning('PDF预览链接不可用')
      return
    }

    if (!isPDFFile(file.fileName)) {
      ElMessage.warning('该文件不是PDF格式')
      return
    }

    const { newWindow = true, width = 1200, height = 800 } = options || {}

    // 获取blob数据并创建对象URL
    const blob = await fetchBlobFromUrl(file.url)
    const objectUrl = createObjectURL(blob)

    if (newWindow) {
      // 在新窗口中打开PDF
      const previewWindow = window.open(
        '',
        '_blank',
        `width=${width},height=${height},scrollbars=yes,resizable=yes,toolbar=yes,menubar=yes`,
      )

      if (!previewWindow) {
        ElMessage.error('无法打开预览窗口，请检查浏览器弹窗设置')
        revokeObjectURL(objectUrl)
        return
      }

      // 使用iframe嵌入PDF
      previewWindow.document.body.innerHTML = `
        <!DOCTYPE html>
        <html>
        <head>
          <title>PDF预览 - ${file.fileName}</title>
          <style>
            body {
              margin: 0;
              padding: 0;
              font-family: Arial, sans-serif;
            }
            .header {
              background: #f5f5f5;
              padding: 10px 20px;
              border-bottom: 1px solid #ddd;
              display: flex;
              justify-content: space-between;
              align-items: center;
            }
            .title {
              font-size: 16px;
              font-weight: bold;
              color: #333;
            }
            .btn {
              background: #409eff;
              color: white;
              border: none;
              padding: 6px 12px;
              border-radius: 4px;
              cursor: pointer;
              font-size: 14px;
            }
            .btn:hover {
              background: #337ecc;
            }
            iframe {
              width: 100%;
              height: calc(100vh - 60px);
              border: none;
            }
          </style>
        </head>
        <body>
          <div class="header">
            <div class="title">${file.fileName}</div>
            <button class="btn" onclick="window.close()">关闭</button>
          </div>
          <iframe src="${objectUrl}" type="application/pdf"></iframe>
          <script>
            // 窗口关闭时释放对象URL
            window.addEventListener('beforeunload', function() {
              URL.revokeObjectURL('${objectUrl}');
            });
          </script>
        </body>
        </html>
      `
    } else {
      // 在当前窗口中打开
      window.location.href = objectUrl
    }

    ElMessage.success('正在打开PDF预览...')
  } catch (error) {
    console.error('PDF预览失败:', error)
    ElMessage.error('PDF预览失败，请稍后重试')
  }
}

/**
 * 智能文件预览（自动判断文件类型）
 * @param file 文件信息
 * @param options 预览选项
 */
export const previewFileAuto = async (
  file: FileInfo,
  options?: {
    newWindow?: boolean
    width?: number
    height?: number
  },
): Promise<void> => {
  try {
    if (!file.url) {
      ElMessage.warning('文件预览链接不可用')
      return
    }
    // URL加上域名
    file.url = import.meta.env.VITE_API_BASE_URL + file.url
    if (isImageFile(file.fileName)) {
      await previewImage(file, options)
    } else if (isPDFFile(file.fileName)) {
      await previewPDF(file, options)
    } else {
      ElMessage.warning(`不支持预览 ${getFileExtension(file.fileName)} 格式的文件`)
    }
  } catch (error) {
    console.error('文件预览失败:', error)
    ElMessage.error('文件预览失败，请稍后重试')
  }
}

/**
 * 全局文件预览方法（兼容原有接口）
 * @param file 文件信息
 * @param options 预览选项
 */
export const previewFile = (
  file: FileInfo,
  options?: {
    newWindow?: boolean
    width?: number
    height?: number
  },
): void => {
  try {
    if (!file.url) {
      ElMessage.warning('文件预览链接不可用')
      return
    }

    if (!isSupportedFileType(file.fileName)) {
      ElMessage.warning(`不支持预览 ${getFileExtension(file.fileName)} 格式的文件`)
      return
    }
    // URL加上域名
    file.url = import.meta.env.VITE_API_BASE_URL + file.url
    const { newWindow = true, width = 1200, height = 800 } = options || {}

    // 构建预览URL
    let previewUrl = file.url

    // 对于Office文档，可以使用在线预览服务
    const extension = getFileExtension(file.fileName)
    if (['.doc', '.docx', '.xls', '.xlsx'].includes(extension)) {
      // 可以集成微软Office在线预览或其他在线预览服务
      // 这里使用简单的下载方式，实际项目中可以替换为在线预览服务
      previewUrl = file.url
    }

    if (newWindow) {
      // 在新窗口中打开
      const windowFeatures = `width=${width},height=${height},scrollbars=yes,resizable=yes,toolbar=no,menubar=no,location=no,status=no`
      const previewWindow = window.open(previewUrl, '_blank', windowFeatures)

      if (!previewWindow) {
        ElMessage.error('无法打开预览窗口，请检查浏览器弹窗设置')
      }
    } else {
      // 在当前窗口中打开
      window.location.href = previewUrl
    }

    ElMessage.success('正在打开文件预览...')
  } catch (error) {
    console.error('文件预览失败:', error)
    ElMessage.error('文件预览失败，请稍后重试')
  }
}

/**
 * blob文件下载方法
 * @param file 文件信息
 * @param options 下载选项
 */
export const downloadBlobFile = async (
  file: FileInfo,
  options?: {
    customFileName?: string
    showProgress?: boolean
  },
): Promise<void> => {
  try {
    if (!file.url) {
      ElMessage.warning('文件下载链接不可用')
      return
    }
    const { customFileName, showProgress = true } = options || {}

    if (showProgress) {
      ElMessage.info('正在准备下载...')
    }

    // 获取blob数据
    const blob = await fetchBlobFromUrl(file.url)
    const objectUrl = createObjectURL(blob)

    // 创建隐藏的下载链接
    const link = document.createElement('a')
    link.href = objectUrl
    link.download = customFileName || file.fileName
    link.style.display = 'none'

    // 添加到DOM并触发点击
    document.body.appendChild(link)
    link.click()

    // 清理DOM和对象URL
    document.body.removeChild(link)

    // 延迟释放对象URL，确保下载完成
    setTimeout(() => {
      revokeObjectURL(objectUrl)
    }, 1000)

    if (showProgress) {
      ElMessage.success('文件下载已开始')
    }
  } catch (error) {
    console.error('blob文件下载失败:', error)
    ElMessage.error('文件下载失败，请稍后重试')
  }
}

/**
 * 全局文件下载方法（兼容原有接口）
 * @param file 文件信息
 * @param options 下载选项
 */
export const downloadFile: (
  file: FileInfo,
  options?: {
    customFileName?: string
    showProgress?: boolean
    useBlob?: boolean
  },
) => Promise<void> = async (
  file: FileInfo,
  options?: {
    customFileName?: string
    showProgress?: boolean
    useBlob?: boolean
  },
): Promise<void> => {
  try {
    if (!file.url) {
      ElMessage.warning('文件下载链接不可用')
      return
    }
    // URL加上域名
    file.url = import.meta.env.VITE_API_BASE_URL + file.url
    const { customFileName, showProgress = true, useBlob = true } = options || {}

    // 如果是blob格式或者指定使用blob方式
    if (useBlob || file.url.startsWith('blob:') || file.url.includes('/blob/')) {
      await downloadBlobFile(file, { customFileName, showProgress })
      return
    }

    // 传统下载方式
    if (showProgress) {
      ElMessage.info('正在准备下载...')
    }

    // 创建隐藏的下载链接
    const link = document.createElement('a')
    link.download = customFileName || file.fileName
    link.style.display = 'none'

    // 添加到DOM并触发点击
    document.body.appendChild(link)
    link.click()

    // 清理DOM
    document.body.removeChild(link)

    if (showProgress) {
      ElMessage.success('文件下载已开始')
    }
  } catch (error) {
    console.error('文件下载失败:', error)
    ElMessage.error('文件下载失败，请稍后重试')
  }
}

/**
 * 批量下载文件
 * @param files 文件列表
 * @param options 下载选项
 */
export const downloadFiles = (
  files: FileInfo[],
  options?: {
    delay?: number
    showProgress?: boolean
  },
): void => {
  try {
    if (!files || files.length === 0) {
      ElMessage.warning('没有可下载的文件')
      return
    }
    const { delay = 500, showProgress = true } = options || {}

    if (showProgress) {
      ElMessage.info(`正在下载 ${files.length} 个文件...`)
    }

    // 依次下载文件，避免浏览器阻止多个下载
    files.forEach((file, index) => {
      setTimeout(() => {
        downloadFile(file, { showProgress: false })
      }, index * delay)
    })

    if (showProgress) {
      ElMessage.success('批量下载已开始')
    }
  } catch (error) {
    console.error('批量下载失败:', error)
    ElMessage.error('批量下载失败，请稍后重试')
  }
}

/**
 * 验证文件上传
 * @param file 文件对象
 * @param options 验证选项
 * @returns 验证结果
 */
export const validateFileUpload = (
  file: File,
  options?: {
    maxSize?: number // 最大文件大小（字节）
    allowedTypes?: string[] // 允许的文件类型
  },
): { valid: boolean; message?: string } => {
  const { maxSize = 10 * 1024 * 1024, allowedTypes = SUPPORTED_EXTENSIONS } = options || {}

  // 检查文件大小
  if (file.size > maxSize) {
    return {
      valid: false,
      message: `文件大小不能超过 ${formatFileSize(maxSize)}`,
    }
  }

  // 检查文件类型
  const extension = getFileExtension(file.name)
  if (!allowedTypes.includes(extension)) {
    return {
      valid: false,
      message: `不支持 ${extension} 格式的文件，支持的格式：${allowedTypes.join(', ')}`,
    }
  }

  return { valid: true }
}

// 导出常用的文件操作方法
export default {
  // 预览方法
  previewFile,
  previewFileAuto,
  previewImage,
  previewPDF,
  // 下载方法
  downloadFile,
  downloadBlobFile,
  downloadFiles,
  // 工具方法
  validateFileUpload,
  getFileExtension,
  isSupportedFileType,
  isImageFile,
  isPDFFile,
  getFileTypeDescription,
  formatFileSize,
  // blob处理方法
  fetchBlobFromUrl,
  createObjectURL,
  revokeObjectURL,
  // 常量
  SUPPORTED_FILE_TYPES,
  SUPPORTED_EXTENSIONS,
  IMAGE_EXTENSIONS,
  PDF_EXTENSIONS,
}
