<template>
  <div class="page-container">
    <el-card>
      <template #header>
        <span>{{ pageTitle }}</span>
      </template>
      <div class="industrial-content">
        <div class="chart-placeholder">
          <p>{{ contentText }}</p>
          <el-descriptions :column="2" border>
            <el-descriptions-item label="行业类型">{{ industryType }}</el-descriptions-item>
            <el-descriptions-item label="监测企业数">{{ companyCount }}</el-descriptions-item>
            <el-descriptions-item label="主要污染物">{{ mainPollutants }}</el-descriptions-item>
            <el-descriptions-item label="治理技术">{{ treatmentTech }}</el-descriptions-item>
          </el-descriptions>
        </div>
      </div>
    </el-card>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import { useRoute } from 'vue-router'

const route = useRoute()

// 根据路由显示不同的页面内容
const pageTitle = computed(() => {
  const path = route.path
  if (path.includes('/steel')) {
    return '工业源 - 钢铁行业'
  } else if (path.includes('/chemical')) {
    return '工业源 - 化工行业'
  } else if (path.includes('/power')) {
    return '工业源 - 电力行业'
  }
  return '工业源'
})

const contentText = computed(() => {
  const path = route.path
  if (path.includes('/steel')) {
    return '钢铁行业污染源监测与治理数据'
  } else if (path.includes('/chemical')) {
    return '化工行业污染源监测与治理数据'
  } else if (path.includes('/power')) {
    return '电力行业污染源监测与治理数据'
  }
  return '工业源污染数据展示'
})

const industryType = computed(() => {
  const path = route.path
  if (path.includes('/steel')) return '钢铁冶炼'
  if (path.includes('/chemical')) return '石油化工'
  if (path.includes('/power')) return '火力发电'
  return '综合工业'
})

const companyCount = computed(() => {
  const path = route.path
  if (path.includes('/steel')) return '156家'
  if (path.includes('/chemical')) return '89家'
  if (path.includes('/power')) return '45家'
  return '290家'
})

const mainPollutants = computed(() => {
  const path = route.path
  if (path.includes('/steel')) return 'SO2, NOx, 颗粒物'
  if (path.includes('/chemical')) return 'VOCs, SO2, NOx'
  if (path.includes('/power')) return 'SO2, NOx, 汞'
  return 'SO2, NOx, 颗粒物, VOCs'
})

const treatmentTech = computed(() => {
  const path = route.path
  if (path.includes('/steel')) return '超低排放改造'
  if (path.includes('/chemical')) return 'RTO, 活性炭吸附'
  if (path.includes('/power')) return '脱硫脱硝除尘'
  return '综合治理技术'
})
</script>

<style scoped>
.page-container {
  padding: 20px;
}
.industrial-content {
  height: 400px;
}
.chart-placeholder {
  height: 100%;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  color: #909399;
  background-color: #f5f7fa;
}
</style>
