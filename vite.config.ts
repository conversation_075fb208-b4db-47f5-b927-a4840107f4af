import { fileURLToPath, URL } from 'node:url'

import { defineConfig, loadEnv } from 'vite'
import vue from '@vitejs/plugin-vue'
import vueJsx from '@vitejs/plugin-vue-jsx'
import AutoImport from 'unplugin-auto-import/vite'
import Components from 'unplugin-vue-components/vite'
import { ElementPlusResolver } from 'unplugin-vue-components/resolvers'
import Icons from 'unplugin-icons/vite'
import IconsResolver from 'unplugin-icons/resolver'
// https://vite.dev/config/
export default defineConfig(({ command, mode }) => {
  // 加载环境变量
  const env = loadEnv(mode, process.cwd(), '')

  return {
    base: env.VITE_PUBLIC_PATH || './',
    plugins: [
      vue(),
      vueJsx(),
      AutoImport({
        imports: ['vue'], // 自动导入 Vue 相关函数，如：ref, reactive, toRef 等
        dts: 'src/auto-import.d.ts', // 生成自动导入的TS声明文件
        resolvers: [
          ElementPlusResolver(), // 自动导入element相关函数，如：ElMessage, ElMessageBox...
          IconsResolver({
            // 自动导入图标组件
            prefix: 'Icon',
          }),
        ],
      }),
      Components({
        dirs: ['src/components'], // 指定自动导入的组件位置，默认是 src/components
        resolvers: [
          ElementPlusResolver(), // 自动导入element相关组件
          // 自动注册图标组件
          // 使用icon时，请采用以下方式才生效。（添加i-eq作为icon前缀）例如：<i-ep-share />
          IconsResolver({
            enabledCollections: ['ep'], //@iconify-json/ep 是 Element Plus 的图标库，所以 IconsResolver 配置了 enabledCollections: ['ep']
          }),
        ],
      }),
      Icons({
        autoInstall: true,
      }),
    ],
    resolve: {
      alias: {
        '@': fileURLToPath(new URL('./src', import.meta.url)),
      },
    },
    server: {
      proxy: {
        '/api': {
          target: process.env.VITE_PROXY_TARGET || 'http://192.168.123.15:10054',
          changeOrigin: true,
          rewrite: (path) => path.replace(/^\/api/, ''),
          configure: (proxy, options) => {
            proxy.on('error', (err, req, res) => {
              console.log('proxy error', err)
            })
            proxy.on('proxyReq', (proxyReq, req, res) => {
              console.log('Sending Request to the Target:', req.method, req.url)
            })
            proxy.on('proxyRes', (proxyRes, req, res) => {
              console.log('Received Response from the Target:', proxyRes.statusCode, req.url)
            })
          },
        },
      },
    },
    build: {
      outDir: env.VITE_OUTPUT_DIR || 'dist',
      sourcemap: mode === 'build-dev', // 开发环境打包时生成sourcemap
      minify: mode !== 'build-dev', // 开发环境打包时不压缩
      rollupOptions: {
        output: {
          // 静态资源分类打包
          chunkFileNames: 'static/js/[name]-[hash].js',
          entryFileNames: 'static/js/[name]-[hash].js',
          assetFileNames: 'static/[ext]/[name]-[hash].[ext]',
        },
      },
    },
  }
})
