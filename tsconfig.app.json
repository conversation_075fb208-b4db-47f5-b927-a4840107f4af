{
  "extends": "@vue/tsconfig/tsconfig.dom.json",
  "include": [
    "env.d.ts",
    "src/**/*",
    "src/**/*.vue",
    "src/**/*.d.ts",
    "./*.d.ts",
    "./auto-imports.d.ts" // 导入上一步生成的配置文件
  ],
  "exclude": ["src/**/__tests__/*"],
  "compilerOptions": {
    "tsBuildInfoFile": "./node_modules/.tmp/tsconfig.app.tsbuildinfo",
    "types": ["element-plus/global"],
    "paths": {
      "@/*": ["./src/*"]
    }
  }
}
