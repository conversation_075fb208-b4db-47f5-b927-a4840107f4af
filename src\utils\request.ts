import axios from 'axios'
import type { AxiosInstance, AxiosResponse, AxiosError } from 'axios'
import { ElMessage, ElMessageBox } from 'element-plus'

// 创建axios实例
const service: AxiosInstance = axios.create({
  baseURL: import.meta.env.VITE_API_BASE_URL || '/api', // API基础URL
  timeout: 15000, // 请求超时时间
  headers: {
    'Content-Type': 'application/json;charset=UTF-8',
  },
})

// 请求拦截器
service.interceptors.request.use(
  (config) => {
    // 添加token
    const token = localStorage.getItem('token') || sessionStorage.getItem('token')
    if (token && config.headers) {
      config.headers.Authorization = `Bearer ${token}`
    }

    // 添加时间戳防止缓存
    if (config.method === 'get') {
      config.params = {
        ...config.params,
        _t: Date.now(),
      }
    }

    // 打印请求信息（开发环境）
    // if (import.meta.env.DEV) {
    //   console.log('🚀 Request:', {
    //     url: config.url,
    //     method: config.method,
    //     params: config.params,
    //     data: config.data,
    //   })
    // }

    return config
  },
  (error: AxiosError) => {
    // 对请求错误做些什么
    console.error('❌ Request Error:', error)
    return Promise.reject(error)
  },
)

// 响应拦截器
service.interceptors.response.use(
  (response: AxiosResponse) => {
    // 对响应数据做点什么
    const { data, status } = response

    // 打印响应信息（开发环境）
    // if (import.meta.env.DEV) {
    //   console.log('✅ Response:', {
    //     url: response.config.url,
    //     status,
    //     data,
    //   })
    // }

    // 根据后端约定的响应格式处理
    if (data.code !== undefined) {
      // 假设后端返回格式为 { code: number, msg: string, data: any }
      if (data.code === 200 || data.code === 0) {
        return data.data || data
      } else if (data.code === 401) {
        // token过期或无效
        handleTokenExpired()
        return Promise.reject(new Error(data.msg || '登录已过期'))
      } else {
        // 其他业务错误
        ElMessage.error(data.msg || '请求失败')
        return Promise.reject(new Error(data.msg || '请求失败'))
      }
    }
    // 如果没有code字段，直接返回data
    return data
  },
  (error: AxiosError) => {
    // 对响应错误做点什么
    console.error('❌ Response Error:', error)
    let message = '网络错误'
    if (error.response) {
      const { status, data } = error.response

      switch (status) {
        case 400:
          message = '请求参数错误'
          break
        case 401:
          message = '未授权，请重新登录'
          handleTokenExpired()
          break
        case 403:
          message = '拒绝访问'
          break
        case 404:
          message = '请求地址不存在'
          break
        case 408:
          message = '请求超时'
          break
        case 500:
          message = '服务器内部错误'
          break
        case 501:
          message = '服务未实现'
          break
        case 502:
          message = '网关错误'
          break
        case 503:
          message = '服务不可用'
          break
        case 504:
          message = '网关超时'
          break
        case 505:
          message = 'HTTP版本不受支持'
          break
        default:
          message = `连接错误${status}`
      }
      // 如果后端返回了错误信息，优先使用后端信息
      if (data && (data as { msg?: string }).msg) {
        message = (data as { msg?: string }).msg!
      }
    } else if (error.request) {
      message = '网络连接异常'
    } else {
      message = error.message || '请求失败'
    }
    ElMessage.error(message)
    return Promise.reject(error)
  },
)

// 处理token过期
const handleTokenExpired = () => {
  // 清除本地存储的token
  localStorage.removeItem('token')
  sessionStorage.removeItem('token')

  // 弹出确认框
  ElMessageBox.confirm('登录状态已过期，您可以继续留在该页面，或者重新登录', '系统提示', {
    confirmButtonText: '重新登录',
    cancelButtonText: '取消',
    type: 'warning',
  })
    .then(() => {
      // 重新登录逻辑
      window.location.reload()
    })
    .catch(() => {
      // 用户选择取消
    })
}

// 封装请求方法
export interface RequestOptions {
  showLoading?: boolean
  showError?: boolean
  timeout?: number
  responseType?: 'json' | 'blob' | 'text' | 'arraybuffer'
  headers?: Record<string, string>
}

// GET请求
export const get = <T = any>(url: string, params?: any, options?: RequestOptions): Promise<T> => {
  return service.get(url, { params, ...options })
}

// POST请求
export const post = <T = any>(url: string, data?: any, options?: RequestOptions): Promise<T> => {
  return service.post(url, data, options)
}

// PUT请求
export const put = <T = any>(url: string, data?: any, options?: RequestOptions): Promise<T> => {
  return service.put(url, data, options)
}

// DELETE请求
export const del = <T = any>(url: string, params?: any, options?: RequestOptions): Promise<T> => {
  return service.delete(url, { params, ...options })
}

// PATCH请求
export const patch = <T = any>(url: string, data?: any, options?: RequestOptions): Promise<T> => {
  return service.patch(url, data, options)
}

// 文件上传
export const upload = <T = any>(
  url: string,
  formData: FormData,
  options?: RequestOptions,
): Promise<T> => {
  return service.post(url, formData, {
    headers: {
      'Content-Type': 'multipart/form-data',
    },
    ...options,
  })
}

// 文件下载
export const download = (url: string, params?: any, filename?: string): Promise<void> => {
  return service
    .get(url, {
      params,
      responseType: 'blob',
    })
    .then((response: any) => {
      const blob = new Blob([response])
      const downloadUrl = window.URL.createObjectURL(blob)
      const link = document.createElement('a')
      link.href = downloadUrl
      link.download = filename || 'download'
      document.body.appendChild(link)
      link.click()
      document.body.removeChild(link)
      window.URL.revokeObjectURL(downloadUrl)
    })
}

export default service
