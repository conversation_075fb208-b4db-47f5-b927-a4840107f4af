import { get, post, put, del } from '@/utils/request'
import type { 
  WarehouseInfo, 
  EntryOrder, 
  EntryOrderSearchParams,
  PageResult,
  DictData
} from './types'

// ==================== 仓库管理 ====================

// 获取仓库列表
export const getWarehouseList = (params?: {
  logicWarehouseName?: string
  validNeed?: string
  includeSupplierWarehouse?: boolean
  keyword?: string
  status?: string
}): Promise<WarehouseInfo[]> => {
  return get<WarehouseInfo[]>('/warehouse/list', params)
}

// 获取仓库详情
export const getWarehouseDetail = (id: string | number): Promise<WarehouseInfo> => {
  return get<WarehouseInfo>(`/warehouse/${id}`)
}

// 创建仓库
export const createWarehouse = (data: Partial<WarehouseInfo>): Promise<WarehouseInfo> => {
  return post<WarehouseInfo>('/warehouse', data)
}

// 更新仓库
export const updateWarehouse = (id: string | number, data: Partial<WarehouseInfo>): Promise<WarehouseInfo> => {
  return put<WarehouseInfo>(`/warehouse/${id}`, data)
}

// 删除仓库
export const deleteWarehouse = (id: string | number): Promise<void> => {
  return del<void>(`/warehouse/${id}`)
}

// 获取仓库类型列表
export const getWarehouseTypes = (): Promise<DictData[]> => {
  return get<DictData[]>('/warehouse/types')
}

// ==================== 入库单管理 ====================

// 获取入库单列表
export const getEntryOrderList = (params: EntryOrderSearchParams): Promise<PageResult<EntryOrder>> => {
  return get<PageResult<EntryOrder>>('/warehouse/entry-orders', params)
}

// 获取入库单详情
export const getEntryOrderDetail = (id: string | number): Promise<EntryOrder> => {
  return get<EntryOrder>(`/warehouse/entry-orders/${id}`)
}

// 创建入库单
export const createEntryOrder = (data: Partial<EntryOrder>): Promise<EntryOrder> => {
  return post<EntryOrder>('/warehouse/entry-orders', data)
}

// 更新入库单
export const updateEntryOrder = (id: string | number, data: Partial<EntryOrder>): Promise<EntryOrder> => {
  return put<EntryOrder>(`/warehouse/entry-orders/${id}`, data)
}

// 删除入库单
export const deleteEntryOrder = (id: string | number): Promise<void> => {
  return del<void>(`/warehouse/entry-orders/${id}`)
}

// 批量删除入库单
export const batchDeleteEntryOrders = (ids: (string | number)[]): Promise<void> => {
  return del<void>('/warehouse/entry-orders/batch', { ids })
}

// 审核入库单
export const approveEntryOrder = (id: string | number, data: {
  status: 'approved' | 'rejected'
  remark?: string
}): Promise<void> => {
  return post<void>(`/warehouse/entry-orders/${id}/approve`, data)
}

// 批量审核入库单
export const batchApproveEntryOrders = (data: {
  ids: (string | number)[]
  status: 'approved' | 'rejected'
  remark?: string
}): Promise<void> => {
  return post<void>('/warehouse/entry-orders/batch-approve', data)
}

// 获取入库单状态列表
export const getEntryOrderStatuses = (): Promise<DictData[]> => {
  return get<DictData[]>('/warehouse/entry-orders/statuses')
}

// 获取业务类型列表
export const getBusinessTypes = (): Promise<DictData[]> => {
  return get<DictData[]>('/warehouse/business-types')
}

// 获取来源类型列表
export const getSourceTypes = (): Promise<DictData[]> => {
  return get<DictData[]>('/warehouse/source-types')
}

// 导出入库单
export const exportEntryOrders = (params: EntryOrderSearchParams): Promise<void> => {
  return get('/warehouse/entry-orders/export', params, {
    responseType: 'blob'
  })
}

// 导入入库单
export const importEntryOrders = (file: File): Promise<{
  successCount: number
  failCount: number
  errors?: string[]
}> => {
  const formData = new FormData()
  formData.append('file', file)
  return post('/warehouse/entry-orders/import', formData, {
    headers: {
      'Content-Type': 'multipart/form-data'
    }
  })
}

// 获取入库单统计数据
export const getEntryOrderStatistics = (params?: {
  startTime?: string
  endTime?: string
  warehouseId?: string | number
}): Promise<{
  totalCount: number
  pendingCount: number
  approvedCount: number
  rejectedCount: number
  totalAmount: number
  monthlyData: Array<{
    month: string
    count: number
    amount: number
  }>
}> => {
  return get('/warehouse/entry-orders/statistics', params)
}
