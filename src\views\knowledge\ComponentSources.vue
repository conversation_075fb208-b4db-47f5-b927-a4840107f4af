<template>
  <div class="page-container">
    <el-card>
      <template #header>
        <div class="card-header">
          <span>特征组分来源库</span>
          <el-button type="primary">添加来源</el-button>
        </div>
      </template>
      
      <el-table :data="tableData" style="width: 100%">
        <el-table-column prop="component" label="特征组分" width="200" />
        <el-table-column prop="source" label="主要来源" width="250" />
        <el-table-column prop="concentration" label="典型浓度范围" width="150" />
        <el-table-column prop="season" label="季节特征" width="120" />
        <el-table-column prop="updateTime" label="更新时间" width="180" />
        <el-table-column label="操作" width="200">
          <template #default="scope">
            <el-button size="small" @click="handleView(scope.row)">查看</el-button>
            <el-button size="small" type="primary" @click="handleEdit(scope.row)">编辑</el-button>
            <el-button size="small" type="danger" @click="handleDelete(scope.row)">删除</el-button>
          </template>
        </el-table-column>
      </el-table>
    </el-card>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'

const tableData = ref([
  {
    id: 1,
    component: '苯系物',
    source: '机动车尾气、工业排放',
    concentration: '5-50 μg/m³',
    season: '夏季偏高',
    updateTime: '2024-01-25'
  },
  {
    id: 2,
    component: '硫酸盐',
    source: '燃煤、工业过程',
    concentration: '10-80 μg/m³',
    season: '冬季偏高',
    updateTime: '2024-01-23'
  }
])

const handleView = (row: any) => {
  console.log('查看来源:', row)
}

const handleEdit = (row: any) => {
  console.log('编辑来源:', row)
}

const handleDelete = (row: any) => {
  console.log('删除来源:', row)
}
</script>

<style scoped>
.page-container {
  padding: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}
</style>
