<template>
  <div class="page-container">
    <el-card>
      <template #header>
        <div class="card-header">
          <span>时间序列分析</span>
          <div class="header-controls">
            <el-select v-model="selectedStation" placeholder="选择监测站">
              <el-option label="市中心站" value="center" />
              <el-option label="工业区站" value="industrial" />
              <el-option label="居民区站" value="residential" />
            </el-select>
            <el-date-picker
              v-model="dateRange"
              type="daterange"
              range-separator="至"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
              style="margin-left: 10px"
            />
          </div>
        </div>
      </template>

      <div class="chart-container">
        <div class="chart-placeholder">
          <el-icon size="64"><TrendCharts /></el-icon>
          <p>时间序列图表</p>
          <p>监测站: {{ selectedStation || '市中心站' }}</p>
          <p>时间范围: {{ formatDateRange() }}</p>
        </div>
      </div>

      <el-row :gutter="20" class="stats-row">
        <el-col :span="6">
          <el-statistic title="平均浓度" :value="45.6" suffix="μg/m³" />
        </el-col>
        <el-col :span="6">
          <el-statistic title="最大值" :value="89.2" suffix="μg/m³" />
        </el-col>
        <el-col :span="6">
          <el-statistic title="最小值" :value="12.3" suffix="μg/m³" />
        </el-col>
        <el-col :span="6">
          <el-statistic title="超标天数" :value="8" suffix="天" />
        </el-col>
      </el-row>
    </el-card>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { TrendCharts } from '@element-plus/icons-vue'

const selectedStation = ref('center')
const dateRange = ref<[Date, Date]>([new Date(Date.now() - 30 * 24 * 60 * 60 * 1000), new Date()])

const formatDateRange = () => {
  if (!dateRange.value || !dateRange.value[0] || !dateRange.value[1]) {
    return '最近30天'
  }
  const start = dateRange.value[0].toLocaleDateString('zh-CN')
  const end = dateRange.value[1].toLocaleDateString('zh-CN')
  return `${start} 至 ${end}`
}
</script>

<style scoped>
.page-container {
  padding: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.header-controls {
  display: flex;
  align-items: center;
}

.chart-container {
  height: 400px;
  border: 1px solid #dcdfe6;
  border-radius: 4px;
  margin-bottom: 20px;
}

.chart-placeholder {
  height: 100%;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  color: #909399;
  background-color: #f5f7fa;
}

.stats-row {
  margin-top: 20px;
}
</style>
