import './assets/main.css'

import { createApp } from 'vue'
import { createPinia } from 'pinia'
import ElementPlus from 'element-plus'
import zhCn from 'element-plus/dist/locale/zh-cn.mjs' // 引入中文语言包
import 'element-plus/dist/index.css'

import App from './App.vue'
import router from './router'
import { fetchDistrictData } from './utils/options'

const app = createApp(App)

app.use(createPinia())
app.use(router)
app.use(ElementPlus, {
  locale: zhCn, // 配置中文语言
})

// 项目初始化时获取地区数据
fetchDistrictData().catch((error) => {
  console.error('初始化地区数据失败:', error)
})

app.mount('#app')
