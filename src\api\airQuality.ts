import { get, post, put, del } from '@/utils/request'
import type { PageResult, DictData } from './types'

// ==================== 空气质量数据可视化 ====================

// 空间分布展示相关接口
export namespace SpatialDistribution {
  // 获取空间分布数据
  export const getData = (params: {
    pollutant: 'pm25' | 'pm10' | 'so2' | 'no2' | 'co' | 'o3'
    date: string
    region?: string
  }): Promise<{
    mapData: Array<{
      stationId: string
      stationName: string
      longitude: number
      latitude: number
      value: number
      level: number
      color: string
    }>
    legend: Array<{
      level: number
      range: string
      color: string
      label: string
    }>
    statistics: {
      average: number
      max: number
      min: number
      excellentCount: number
      goodCount: number
      pollutedCount: number
    }
  }> => {
    return get('/air-quality/spatial-distribution', params)
  }

  // 获取监测站点列表
  export const getStations = (params?: {
    region?: string
    status?: 'active' | 'inactive'
  }): Promise<Array<{
    id: string
    name: string
    code: string
    longitude: number
    latitude: number
    region: string
    type: string
    status: string
  }>> => {
    return get('/air-quality/stations', params)
  }
}

// 时间序列分析相关接口
export namespace TimeSeries {
  // 获取时间序列数据
  export const getData = (params: {
    stationId: string
    pollutant: string
    startDate: string
    endDate: string
    timeType: 'hour' | 'day' | 'month'
  }): Promise<{
    chartData: Array<{
      time: string
      value: number
      level: number
    }>
    statistics: {
      average: number
      max: number
      min: number
      exceedanceCount: number
      exceedanceRate: number
    }
    forecast?: Array<{
      time: string
      value: number
      confidence: number
    }>
  }> => {
    return get('/air-quality/time-series', params)
  }

  // 获取多站点对比数据
  export const getComparisonData = (params: {
    stationIds: string[]
    pollutant: string
    startDate: string
    endDate: string
  }): Promise<{
    chartData: Array<{
      time: string
      [stationId: string]: number | string
    }>
    stationInfo: Array<{
      id: string
      name: string
      color: string
    }>
  }> => {
    return get('/air-quality/time-series/comparison', params)
  }
}

// 区域排名展示相关接口
export namespace RegionalRanking {
  // 获取区域排名数据
  export const getData = (params: {
    pollutant: string
    timeRange: string // 'today' | 'week' | 'month' | 'year'
    rankType: 'aqi' | 'concentration'
    region?: string
  }): Promise<{
    rankingData: Array<{
      rank: number
      regionName: string
      value: number
      level: number
      change: number // 相比上期变化
      trend: 'up' | 'down' | 'stable'
    }>
    chartData: {
      categories: string[]
      series: Array<{
        name: string
        data: number[]
        color: string
      }>
    }
    summary: {
      totalRegions: number
      excellentCount: number
      goodCount: number
      pollutedCount: number
      averageValue: number
    }
  }> => {
    return get('/air-quality/regional-ranking', params)
  }

  // 获取区域详细信息
  export const getRegionDetail = (params: {
    regionId: string
    pollutant: string
    timeRange: string
  }): Promise<{
    regionInfo: {
      id: string
      name: string
      level: string
      population: number
      area: number
    }
    currentData: {
      value: number
      level: number
      rank: number
      trend: string
    }
    historicalData: Array<{
      time: string
      value: number
      rank: number
    }>
    stationData: Array<{
      stationId: string
      stationName: string
      value: number
      level: number
    }>
  }> => {
    return get(`/air-quality/regional-ranking/detail`, params)
  }
}

// 同步环比分析相关接口
export namespace ComparisonAnalysis {
  // 获取同比环比数据
  export const getData = (params: {
    pollutant: string
    region: string
    analysisType: 'yoy' | 'mom' | 'wow' // 同比/环比/周比
    currentPeriod: string
    comparisonPeriod?: string
  }): Promise<{
    comparisonData: {
      current: {
        period: string
        value: number
        level: number
      }
      comparison: {
        period: string
        value: number
        level: number
      }
      change: {
        absolute: number
        percentage: number
        trend: 'improve' | 'worsen' | 'stable'
      }
    }
    chartData: {
      categories: string[]
      currentSeries: number[]
      comparisonSeries: number[]
    }
    analysis: {
      summary: string
      factors: string[]
      suggestions: string[]
    }
  }> => {
    return get('/air-quality/comparison-analysis', params)
  }

  // 获取多污染物对比分析
  export const getMultiPollutantComparison = (params: {
    pollutants: string[]
    region: string
    timeRange: string
    analysisType: 'yoy' | 'mom'
  }): Promise<{
    comparisonData: Array<{
      pollutant: string
      pollutantName: string
      current: number
      comparison: number
      change: number
      changePercentage: number
      trend: string
    }>
    chartData: {
      categories: string[]
      series: Array<{
        name: string
        current: number[]
        comparison: number[]
      }>
    }
  }> => {
    return get('/air-quality/comparison-analysis/multi-pollutant', params)
  }
}

// ==================== 通用接口 ====================

// 获取污染物列表
export const getPollutantList = (): Promise<DictData[]> => {
  return get<DictData[]>('/air-quality/pollutants')
}

// 获取AQI等级配置
export const getAqiLevels = (): Promise<Array<{
  level: number
  name: string
  color: string
  range: string
  description: string
}>> => {
  return get('/air-quality/aqi-levels')
}

// 获取数据更新时间
export const getDataUpdateTime = (): Promise<{
  lastUpdateTime: string
  nextUpdateTime: string
  updateFrequency: string
}> => {
  return get('/air-quality/update-time')
}

// 数据导出
export const exportData = (params: {
  type: 'spatial' | 'timeSeries' | 'ranking' | 'comparison'
  format: 'excel' | 'csv' | 'pdf'
  [key: string]: any
}): Promise<void> => {
  return get('/air-quality/export', params, {
    responseType: 'blob'
  })
}
