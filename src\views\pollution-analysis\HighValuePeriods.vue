<template>
  <div class="page-container">
    <el-card>
      <template #header>
        <span>高值时段分析</span>
      </template>
      <div class="periods-content">
        <div class="chart-placeholder">
          <el-icon size="64"><Clock /></el-icon>
          <p>高值时段分析</p>
        </div>
      </div>
    </el-card>
  </div>
</template>

<script setup lang="ts">
import { Clock } from '@element-plus/icons-vue'
</script>

<style scoped>
.page-container {
  padding: 20px;
}
.periods-content {
  height: 400px;
}
.chart-placeholder {
  height: 100%;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  color: #909399;
  background-color: #f5f7fa;
}
</style>
