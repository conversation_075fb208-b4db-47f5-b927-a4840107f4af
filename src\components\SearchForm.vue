<template>
  <div class="search-form">
    <el-form
      ref="formRef"
      :model="formData"
      :rules="formRules"
      :label-width="labelWidth"
      class="search-form-content"
      label-position="top"
      size="small"
    >
      <el-row :gutter="gutter">
        <!-- 显示的表单项 -->
        <template v-for="(item, index) in displayItems" :key="item.prop">
          <el-col v-bind="item.colGrid || { span: defaultSpan }">
            <el-form-item :prop="item.prop" :rules="item.rules" v-bind="item.formItem || {}">
              <!-- 输入框 -->
              <el-input
                v-if="item.type === 'input'"
                v-model="formData[item.prop]"
                v-bind="item.attrs || {}"
                v-on="getEvents(item)"
              />

              <!-- 选择器 -->
              <el-select
                v-else-if="item.type === 'select'"
                v-model="formData[item.prop]"
                v-bind="item.attrs || {}"
                v-on="getEvents(item)"
              >
                <el-option
                  v-for="option in options[item.prop] || []"
                  :key="option.value"
                  :label="option.label"
                  :value="option.value"
                />
              </el-select>

              <!-- 日期选择器 -->
              <el-date-picker
                v-else-if="item.type === 'date'"
                v-model="formData[item.prop]"
                v-bind="item.attrs || {}"
                v-on="getEvents(item)"
              />

              <!-- 日期范围选择器 -->
              <el-date-picker
                v-else-if="item.type === 'daterange'"
                v-model="formData[item.prop]"
                type="daterange"
                v-bind="item.attrs || {}"
                v-on="getEvents(item)"
              />

              <!-- 数字输入框 -->
              <el-input-number
                v-else-if="item.type === 'number'"
                v-model="formData[item.prop]"
                v-bind="item.attrs || {}"
                v-on="getEvents(item)"
              />

              <!-- 时间选择器 -->
              <el-time-picker
                v-else-if="item.type === 'time'"
                v-model="formData[item.prop]"
                v-bind="item.attrs || {}"
                v-on="getEvents(item)"
              />

              <!-- 开关 -->
              <el-switch
                v-else-if="item.type === 'switch'"
                v-model="formData[item.prop]"
                v-bind="item.attrs || {}"
                v-on="getEvents(item)"
              />

              <!-- 单选框组 -->
              <el-radio-group
                v-else-if="item.type === 'radio'"
                v-model="formData[item.prop]"
                v-bind="item.attrs || {}"
                v-on="getEvents(item)"
              >
                <el-radio
                  v-for="option in options[item.prop] || []"
                  :key="option.value"
                  :label="option.value"
                >
                  {{ option.label }}
                </el-radio>
              </el-radio-group>

              <!-- 多选框组 -->
              <el-checkbox-group
                v-else-if="item.type === 'checkbox'"
                v-model="formData[item.prop]"
                v-bind="item.attrs || {}"
                v-on="getEvents(item)"
              >
                <el-checkbox
                  v-for="option in options[item.prop] || []"
                  :key="option.value"
                  :label="option.value"
                >
                  {{ option.label }}
                </el-checkbox>
              </el-checkbox-group>

              <!-- 文本域 -->
              <el-input
                v-else-if="item.type === 'textarea'"
                v-model="formData[item.prop]"
                type="textarea"
                v-bind="item.attrs || {}"
                v-on="getEvents(item)"
              />

              <!-- 级联选择器 -->
              <el-cascader
                v-else-if="item.type === 'cascader'"
                v-model="formData[item.prop]"
                v-bind="item.attrs || {}"
                v-on="getEvents(item)"
              />
            </el-form-item>
          </el-col>
        </template>
      </el-row>
    </el-form>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch } from 'vue'
import type { FormRules } from 'element-plus'

// 定义表单项类型
export interface FormItem {
  type:
    | 'input'
    | 'select'
    | 'date'
    | 'daterange'
    | 'number'
    | 'time'
    | 'switch'
    | 'radio'
    | 'checkbox'
    | 'textarea'
    | 'cascader'
  prop: string
  formItem?: Record<string, any>
  attrs?: Record<string, any>
  on?: Record<string, Function>
  span?: number
  hidden?: boolean
  colGrid?: Record<string, any>
  rules?: any[]
}

// 定义组件属性
interface Props {
  config: FormItem[]
  modelValue?: Record<string, any>
  rules?: FormRules
  labelWidth?: string
  defaultSpan?: number
  gutter?: number
}

// 定义事件
interface Emits {
  (e: 'update:modelValue', value: Record<string, any>): void
}

const props = withDefaults(defineProps<Props>(), {
  labelWidth: '100px',
  defaultSpan: 6,
  gutter: 20,
})

const emit = defineEmits<Emits>()

// 响应式数据
const formRef = ref()
const formData = ref<Record<string, any>>({})
const options = ref<Record<string, any[]>>({})

// 确保formData有正确的初始结构
const ensureFormDataStructure = () => {
  props.config.forEach((item) => {
    if (!(item.prop in formData.value)) {
      formData.value[item.prop] = item.attrs?.multiple ? [] : ''
    }
  })
}

// 计算属性
const formRules = computed(() => props.rules || {})

const displayItems = computed(() => {
  return props.config.filter((item) => !item.hidden)
})

// 初始化表单数据
const initFormData = () => {
  props.config.forEach((item) => {
    if (props.modelValue && Object.prototype.hasOwnProperty.call(props.modelValue, item.prop)) {
      formData.value[item.prop] = props.modelValue[item.prop]
    } else {
      const defaultValue = item.attrs?.multiple ? [] : ''
      formData.value[item.prop] = defaultValue
    }
  })
}

// 监听外部数据变化
watch(
  () => props.modelValue,
  (newVal) => {
    if (newVal) {
      // 确保所有配置的字段都被初始化
      props.config.forEach((item) => {
        if (newVal[item.prop] !== undefined) {
          formData.value[item.prop] = newVal[item.prop]
        } else if (formData.value[item.prop] === undefined) {
          formData.value[item.prop] = item.attrs?.multiple ? [] : ''
        }
      })
    }
  },
  { deep: true, immediate: true },
)

// 监听表单数据变化，向外传递
watch(
  formData,
  (newVal) => {
    emit('update:modelValue', { ...newVal })
  },
  { deep: true, immediate: true },
)

// 处理事件
const getEvents = (item: FormItem) => {
  const events = { ...item.on }

  // 如果有remoteMethod，需要特殊处理
  if (item.attrs?.remoteMethod && item.type === 'select') {
    events.remoteMethod = async (query: string) => {
      if (item.attrs?.remoteMethod) {
        await item.attrs.remoteMethod(query, options.value, item.prop)
      }
    }
  }
  return events
}

// 设置选项数据
const setOptions = (prop: string, optionList: any[]) => {
  options.value[prop] = optionList
}

// 获取表单数据
const getFormData = () => {
  return { ...formData.value }
}

// 重置表单
const resetFields = () => {
  formRef.value?.resetFields()
  props.config.forEach((item) => {
    formData.value[item.prop] = item.attrs?.multiple ? [] : ''
  })
}

// 表单验证
const validate = () => {
  return formRef.value?.validate()
}

// 清除验证
const clearValidate = (props?: string | string[]) => {
  formRef.value?.clearValidate(props)
}

// 暴露方法给父组件
defineExpose({
  setOptions,
  getFormData,
  resetFields,
  validate,
  clearValidate,
  formRef,
})

// 初始化
ensureFormDataStructure()
initFormData()
</script>

<style scoped>
.search-form {
  padding: 16px;
  width: 100%;
}

.search-form-content {
  width: 100%;
}

:deep(.el-form-item) {
  margin-bottom: 18px;
}

:deep(.el-form-item__label) {
  color: #606266;
  font-weight: normal;
  margin-bottom: 2px;
}
:deep(.el-cascader) {
  width: 100%;
}
</style>
