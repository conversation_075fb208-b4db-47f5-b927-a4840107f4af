<template>
  <div class="page-container">
    <el-card>
      <template #header>
        <span>气象关联分析</span>
      </template>
      <div class="correlation-content">
        <div class="chart-placeholder">
          <el-icon size="64"><Cloudy /></el-icon>
          <p>气象关联分析</p>
        </div>
      </div>
    </el-card>
  </div>
</template>

<script setup lang="ts">
import { Cloudy } from '@element-plus/icons-vue'
</script>

<style scoped>
.page-container {
  padding: 20px;
}
.correlation-content {
  height: 400px;
}
.chart-placeholder {
  height: 100%;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  color: #909399;
  background-color: #f5f7fa;
}
</style>
