<template>
  <el-dialog
    v-model="dialogVisible"
    title="排查案例详情"
    width="60%"
    :before-close="handleClose"
    destroy-on-close
  >
    <div v-if="detailData" class="detail-container">
      <!-- 基本信息 -->
      <el-card class="detail-card">
        <template #header>
          <div class="card-header">
            <span>基本信息</span>
          </div>
        </template>

        <el-row :gutter="20">
          <el-col :span="12">
            <div class="detail-item">
              <label>案例名称：</label>
              <span>{{ detailData.checkCaseName }}</span>
            </div>
          </el-col>
          <el-col :span="12">
            <div class="detail-item">
              <label>适用地区：</label>
              <span>{{ detailData.applicableRegionName }}</span>
            </div>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="12">
            <div class="detail-item">
              <label>排查开始时间：</label>
              <span>{{ detailData.investigationStartDate }}</span>
            </div>
          </el-col>
          <el-col :span="12">
            <div class="detail-item">
              <label>排查结束时间：</label>
              <span>{{ detailData.investigationEndDate }}</span>
            </div>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="12">
            <div class="detail-item">
              <label>排查对象：</label>
              <span>{{ detailData.investigationTarget }}</span>
            </div>
          </el-col>
          <el-col :span="12">
            <div class="detail-item">
              <label>排查来源：</label>
              <span>{{ detailData.sourceName }}</span>
            </div>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="12">
            <div class="detail-item">
              <label>排查类型：</label>
              <span>{{ detailData.investigationTypeName }}</span>
            </div>
          </el-col>
          <el-col :span="12">
            <div class="detail-item">
              <label>关联政策：</label>
              <span>{{ detailData.policyIdsName }}</span>
            </div>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="12">
            <div class="detail-item">
              <label>排查方法：</label>
              <span>{{ detailData.investigationMethod }}</span>
            </div>
          </el-col>
          <el-col :span="12">
            <div class="detail-item">
              <label>排查范围：</label>
              <span>{{ detailData.investigationScope }}</span>
            </div>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="24">
            <div class="detail-item">
              <label>案例分析总结：</label>
              <div class="detail-content">{{ detailData.caseSummary }}</div>
            </div>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="12">
            <div class="detail-item">
              <label>状态：</label>
              <el-tag :type="getStatusTagType(detailData.dataStatus)" size="small">
                {{ detailData.dataStatusName }}
              </el-tag>
            </div>
          </el-col>
          <el-col :span="12">
            <div class="detail-item">
              <label>更新时间：</label>
              <span>{{ detailData.updateTime }}</span>
            </div>
          </el-col>
        </el-row>

        <el-row v-if="detailData.remarks" :gutter="20">
          <el-col :span="24">
            <div class="detail-item">
              <label>备注：</label>
              <div class="detail-content">{{ detailData.remarks }}</div>
            </div>
          </el-col>
        </el-row>
      </el-card>

      <!-- 问题详情 -->
      <el-card
        v-if="detailData.problemList && detailData.problemList.length > 0"
        class="detail-card"
      >
        <template #header>
          <div class="card-header">
            <span>问题详情</span>
          </div>
        </template>

        <div v-for="(problem, index) in detailData.problemList" :key="index" class="problem-item">
          <div class="problem-header">
            <span>问题 {{ index + 1 }}</span>
          </div>

          <el-row :gutter="20">
            <el-col :span="12">
              <div class="detail-item">
                <label>问题类型：</label>
                <span>{{ problem.problemTypeName }}</span>
              </div>
            </el-col>
            <el-col :span="12">
              <div class="detail-item">
                <label>整改结果：</label>
                <el-tag :type="getRectificationTagType(problem.rectificationResult)" size="small">
                  {{ problem.rectificationResultName }}
                </el-tag>
              </div>
            </el-col>
          </el-row>

          <el-row :gutter="20">
            <el-col :span="12">
              <div class="detail-item">
                <label>主要问题描述：</label>
                <div class="detail-content">{{ problem.mainProblemDescription }}</div>
              </div>
            </el-col>
            <el-col :span="12">
              <div class="detail-item">
                <label>依据法规条款：</label>
                <div class="detail-content">{{ problem.regulationClause }}</div>
              </div>
            </el-col>
          </el-row>

          <el-row :gutter="20">
            <el-col :span="12">
              <div class="detail-item">
                <label>处理措施：</label>
                <div class="detail-content">{{ problem.measures }}</div>
              </div>
            </el-col>
            <el-col :span="12">
              <div class="detail-item">
                <label>整改时限：</label>
                <span>{{ problem.rectificationDeadline }}</span>
              </div>
            </el-col>
          </el-row>

          <el-row v-if="problem.remarks" :gutter="20">
            <el-col :span="24">
              <div class="detail-item">
                <label>备注：</label>
                <div class="detail-content">{{ problem.remarks }}</div>
              </div>
            </el-col>
          </el-row>
        </div>
      </el-card>

      <!-- 附件信息 -->
      <el-card v-if="detailData.fileList && detailData.fileList.length > 0" class="detail-card">
        <template #header>
          <div class="card-header">
            <span>附件信息</span>
          </div>
        </template>

        <div class="file-list">
          <div v-for="(file, index) in detailData.fileList" :key="index" class="file-item">
            <el-icon class="file-icon">
              <Document />
            </el-icon>
            <span class="file-name">{{ file.fileName }}</span>
            <div class="file-actions">
              <el-button size="small" type="primary" link @click="previewFile(file)">
                预览
              </el-button>
              <el-button size="small" type="primary" link @click="downloadFile(file)">
                下载
              </el-button>
            </div>
          </div>
        </div>
      </el-card>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">关闭</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, watch } from 'vue'
import { Document } from '@element-plus/icons-vue'
import type { CheckCaseInfo, CheckCaseFile } from '@/api/types'
import { previewFileAuto, downloadFile as downloadFileUtil } from '@/utils/fileUtils'

interface Props {
  modelValue: boolean
  detailData?: CheckCaseInfo | null
}

interface Emits {
  (e: 'update:modelValue', value: boolean): void
}

const props = withDefaults(defineProps<Props>(), {
  detailData: null,
})

const emit = defineEmits<Emits>()

const dialogVisible = ref(false)

// 监听弹窗显示状态
watch(
  () => props.modelValue,
  (val) => {
    dialogVisible.value = val
  },
  { immediate: true },
)

watch(dialogVisible, (val) => {
  emit('update:modelValue', val)
})

// 获取状态标签类型
const getStatusTagType = (status: number) => {
  switch (status) {
    case 1:
      return 'success' // 正常
    case 2:
      return 'info' // 暂存
    case 3:
      return 'warning' // 待审核
    case -1:
      return 'danger' // 失效
    case -3:
      return 'danger' // 不通过
    default:
      return 'info'
  }
}

// 获取整改结果标签类型
const getRectificationTagType = (result: number | string) => {
  switch (Number(result)) {
    case 1:
      return 'success' // 已完成整改
    case 2:
      return 'warning' // 整改中
    case 3:
      return 'danger' // 未整改
    default:
      return 'info'
  }
}

// 预览文件
const previewFile = (file: CheckCaseFile) => {
  if (file.url) {
    previewFileAuto({
      fileId: file.fileId,
      fileName: file.fileName,
      url: file.url,
    })
  }
}

// 下载文件
const downloadFile = (file: CheckCaseFile) => {
  if (file.url) {
    downloadFileUtil({
      fileId: file.fileId,
      fileName: file.fileName,
      url: file.url,
    })
  }
}

// 关闭弹窗
const handleClose = () => {
  dialogVisible.value = false
}
</script>

<style scoped>
.detail-container {
  max-height: 70vh;
  overflow-y: auto;
}

.detail-card {
  margin-bottom: 20px;
}

.detail-card:last-child {
  margin-bottom: 0;
}

.card-header {
  font-weight: bold;
  font-size: 16px;
}

.detail-item {
  margin-bottom: 15px;
  display: flex;
  align-items: flex-start;
}

.detail-item label {
  font-weight: bold;
  min-width: 120px;
  color: #606266;
  margin-right: 10px;
}

.detail-item span {
  color: #303133;
  word-break: break-all;
}

.detail-content {
  color: #303133;
  line-height: 1.6;
  word-break: break-all;
  white-space: pre-wrap;
}

.problem-item {
  border: 1px solid #f0f0f0;
  border-radius: 4px;
  padding: 20px;
  margin-bottom: 20px;
  background-color: #fafafa;
}

.problem-item:last-child {
  margin-bottom: 0;
}

.problem-header {
  font-weight: bold;
  font-size: 14px;
  margin-bottom: 15px;
  color: #409eff;
}

.file-list {
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.file-item {
  display: flex;
  align-items: center;
  padding: 10px;
  border: 1px solid #e4e7ed;
  border-radius: 4px;
  background-color: #f9f9f9;
}

.file-icon {
  margin-right: 10px;
  color: #409eff;
}

.file-name {
  flex: 1;
  color: #303133;
  word-break: break-all;
}

.file-actions {
  display: flex;
  gap: 10px;
}

.dialog-footer {
  text-align: right;
}
</style>
